# 收集其他源文件
file(GLOB_RECURSE VIDEO_AI_SOURCES
    "${CMAKE_CURRENT_SOURCE_DIR}/src/*.cpp"
    "${CMAKE_CURRENT_SOURCE_DIR}/src/*/*.cpp"
    "${CMAKE_CURRENT_SOURCE_DIR}/src/*/*/*.cpp"
)

# 收集头文件
file(GLOB_RECURSE VIDEO_AI_HEADERS
    "${CMAKE_CURRENT_SOURCE_DIR}/include/*.h"
    "${CMAKE_CURRENT_SOURCE_DIR}/include/*/*.h"
    "${CMAKE_CURRENT_SOURCE_DIR}/include/*/*/*.h"
)

# 设置 CMake 的编码
if(MSVC)
    # 为 MSVC 编译器设置 UTF-8 编码
    add_compile_options(
        /utf-8                   # 强制使用 UTF-8
        /wd4819                  # 禁用 code page 警告
        /DWIN32_LEAN_AND_MEAN   # 减少 Windows 头文件包含
    )

    # 添加 Unicode 定义
    add_compile_definitions(
        _UNICODE
        UNICODE
        NOMINMAX                 # 避免 Windows 宏与 STL 冲突
    )
endif()

# 确保源文件使用 UTF-8 编码
if(CMAKE_CXX_COMPILER_ID MATCHES "GNU|Clang")
    add_compile_options(-finput-charset=UTF-8)
endif()

if(MSVC)
    add_compile_definitions(
        _SILENCE_CXX17_CODECVT_HEADER_DEPRECATION_WARNING
        HAVE_SNPRINTF
        _CRT_SECURE_NO_WARNINGS
    )
endif()

# Ensure Python definitions are set correctly
add_compile_definitions(
    PYTHON_EXECUTABLE="${Python3_EXECUTABLE}"
    PYTHON_INCLUDE_DIR="${Python3_INCLUDE_DIRS}"
    PYTHON_LIBRARY="${Python3_LIBRARIES}"
)

# 创建AiVideoCore库, 当前局限于linux中使用vcpkg的ffmpeg不是在fpic模式下编译，因此只能使用静态库
if(WIN32)
    add_library(AiVideoCore SHARED ${VIDEO_AI_SOURCES} ${VIDEO_AI_HEADERS})
else()
    add_library(AiVideoCore STATIC ${VIDEO_AI_SOURCES} ${VIDEO_AI_HEADERS})
endif()
target_include_directories(
    AiVideoCore
    PUBLIC
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
    $<INSTALL_INTERFACE:include>
)

# 定义导出宏
target_compile_definitions(AiVideoCore
    PRIVATE AIVIDEOCORE_EXPORTS
    PUBLIC AIVIDEOCORE_SHARED
)

target_link_libraries(AiVideoCore
    PUBLIC
    VisionFlow
    pybind11::embed
    Python3::Python
)

# 链接 OpenCV 库
find_package(OpenCV CONFIG REQUIRED)
target_link_libraries(AiVideoCore PUBLIC ${OpenCV_LIBS})

# 链接 Boost 库
find_package(boost_filesystem CONFIG REQUIRED)
target_link_libraries(AiVideoCore PUBLIC Boost::filesystem)
find_package(boost_log CONFIG REQUIRED)
target_link_libraries(AiVideoCore PUBLIC Boost::log)

# 链接 jsoncpp 库
find_package(jsoncpp CONFIG REQUIRED)

# 添加选项来控制是否使用静态链接
option(USE_STATIC_JSONCPP "Use static linking for jsoncpp" ON)

if(USE_STATIC_JSONCPP)
    message(STATUS "Configuring jsoncpp for static linking...")

    if(WIN32)
        # Windows 平台：查找静态库
        if(TARGET JsonCpp::JsonCpp_static)
            target_link_libraries(AiVideoCore PUBLIC JsonCpp::JsonCpp_static)
            message(STATUS "Using static jsoncpp library: JsonCpp::JsonCpp_static")
        else()
            # 手动查找静态库文件
            find_library(JSONCPP_STATIC_LIB
                NAMES jsoncpp_static jsoncpp
                PATHS ${jsoncpp_DIR}/../../../lib
                NO_DEFAULT_PATH
            )

            if(JSONCPP_STATIC_LIB)
                target_link_libraries(AiVideoCore PUBLIC ${JSONCPP_STATIC_LIB})
                message(STATUS "Using static jsoncpp library: ${JSONCPP_STATIC_LIB}")

                # 添加静态链接的编译定义
                target_compile_definitions(AiVideoCore PRIVATE JSON_DLL=0)
            else()
                message(WARNING "Static jsoncpp not found, falling back to dynamic linking")
                target_link_libraries(AiVideoCore PUBLIC JsonCpp::JsonCpp)
            endif()
        endif()
    else()
        # Linux 平台：通常默认就是静态库
        target_link_libraries(AiVideoCore PUBLIC JsonCpp::JsonCpp)
        message(STATUS "Using jsoncpp library: JsonCpp::JsonCpp (typically static on Linux)")
    endif()
else()
    # 使用动态链接
    message(STATUS "Configuring jsoncpp for dynamic linking...")
    target_link_libraries(AiVideoCore PUBLIC JsonCpp::JsonCpp)

    # 添加动态链接的编译定义
    if(WIN32)
        target_compile_definitions(AiVideoCore PRIVATE JSON_DLL=1)
    endif()
endif()

# # 链接MQTT库（如果可用）
find_package(PahoMqttCpp CONFIG REQUIRED)
if(WIN32)
    target_link_libraries(AiVideoCore PUBLIC PahoMqttCpp::paho-mqttpp3 PahoMqttCpp::paho-mqttpp3-shared)
else()
    target_link_libraries(AiVideoCore PUBLIC PahoMqttCpp::paho-mqttpp3 PahoMqttCpp::paho-mqttpp3-static)
endif()


target_compile_definitions(AiVideoCore PRIVATE VFLOW_ENABLE_OPENCV NON_BLOCKING_MODE)

# 安装目标
install(
    TARGETS AiVideoCore
    EXPORT AiVideoCoreTargets
    ARCHIVE DESTINATION lib
    RUNTIME DESTINATION release
    LIBRARY DESTINATION lib
)

# 安装导出目标
install(
    EXPORT AiVideoCoreTargets
    FILE AiVideoCoreTargets.cmake
    NAMESPACE AiVideoCore::
    DESTINATION lib/cmake/AiVideoCore
)

# 安装头文件
install(
    DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/include/
    DESTINATION include
    FILES_MATCHING PATTERN "*.h"
)

# 创建脚本目录
install(
    DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/scripts/
    DESTINATION release/scripts
    FILES_MATCHING PATTERN "*.py"
)


















