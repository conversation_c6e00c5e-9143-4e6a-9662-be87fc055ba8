#include "counting_plugin.h"
#include <iostream>
#include <algorithm>

namespace ai {
namespace plugins {

CountingPlugin::CountingPlugin()
    : TaskPlugin("CountingPlugin"),
      required_frames_(1),
      frame_count_(0),
      total_count_(0),
      line_y_(0.5f),
      line_x_(0.5f) {

    // 设置默认参数（使用基类的params_）
    TaskPlugin::set_params(get_default_params());
}

bool CountingPlugin::initialize() {
    std::cout << "CountingPlugin::initialize - 开始初始化" << std::endl;

    // 重置状态
    reset();

    // 加载参数
    load_parameters();

    std::cout << "CountingPlugin::initialize - 计数插件初始化完成，需要帧数: " << required_frames_ << "，参数: "
              << "line_y=" << line_y_ << ", "
              << "line_x=" << line_x_ << std::endl;

    return true;
}

void CountingPlugin::load_parameters() {
    // 获取基类的参数
    auto params = TaskPlugin::get_params();

    std::cout << "CountingPlugin::load_parameters - 开始加载参数" << std::endl;

    // 打印所有参数
    std::cout << "当前参数列表:" << std::endl;
    for (const auto& [key, value] : params) {
        std::cout << "  " << key << ": " << value << std::endl;
    }

    // 从参数中读取需要的帧数
    if (params.find("required_frames") != params.end()) {
        try {
            required_frames_ = std::stoi(params["required_frames"]);
            // 确保帧数在合理范围内
            if (required_frames_ < 1) {
                required_frames_ = 1;
            } else if (required_frames_ > 100) {
                required_frames_ = 100;
            }
            std::cout << "设置required_frames = " << required_frames_ << std::endl;
        } catch (...) {
            required_frames_ = 1; // 如果转换失败，使用默认值
            std::cout << "解析required_frames失败，使用默认值: " << required_frames_ << std::endl;
        }
    }

    // 从参数中读取垂直计数线位置
    if (params.find("line_y") != params.end()) {
        try {
            float old_value = line_y_;
            line_y_ = std::stof(params["line_y"]);
            std::cout << "设置line_y = " << line_y_ << " (原值: " << old_value << ")" << std::endl;
        } catch (...) {
            line_y_ = 0.5f; // 默认在画面中间
            std::cout << "解析line_y失败，使用默认值: " << line_y_ << std::endl;
        }
    } else {
        std::cout << "未找到line_y参数，保持当前值: " << line_y_ << std::endl;
    }

    // 从参数中读取水平计数线位置
    if (params.find("line_x") != params.end()) {
        try {
            float old_value = line_x_;
            line_x_ = std::stof(params["line_x"]);
            std::cout << "设置line_x = " << line_x_ << " (原值: " << old_value << ")" << std::endl;
        } catch (...) {
            line_x_ = 0.5f; // 默认在画面中间
            std::cout << "解析line_x失败，使用默认值: " << line_x_ << std::endl;
        }
    } else {
        std::cout << "未找到line_x参数，保持当前值: " << line_x_ << std::endl;
    }

    std::cout << "CountingPlugin::load_parameters - 参数加载完成" << std::endl;
}

bool CountingPlugin::process_batch(const std::vector<cv::Mat>& frames,
                                 const std::vector<std::vector<tracking::strack>>& tracks_list,
                                 ai::FrameResult& result) {
    if (!is_enabled() || frames.empty() || tracks_list.empty()) {
        return false;
    }

    // 获取当前帧和跟踪结果
    const cv::Mat& frame = frames[0];
    const auto& tracks = tracks_list[0];

    // 更新帧计数
    frame_count_++;

    // 获取垂直计数线位置
    int line_y = static_cast<int>(line_y_ * frame.rows);

    // 获取水平计数线位置
    int line_x = static_cast<int>(line_x_ * frame.cols);

    // 每100帧打印一次当前计数线位置
    if (frame_count_ % 100 == 0 || frame_count_ == 1) {
        std::cout << "当前帧: " << frame_count_
                  << ", 计数线位置 - 水平线: " << line_x << " (比例: " << line_x_
                  << "), 垂直线: " << line_y << " (比例: " << line_y_ << ")" << std::endl;
    }

    // 处理当前帧的跟踪结果
    for (const auto& track : tracks) {
        int track_id = track.track_id;
        std::string cls = track.detect_class;

        // 获取目标中心点
        float x = track.tlwh.x + track.tlwh.width / 2;
        float y = track.tlwh.y + track.tlwh.height / 2;

        // 检查是否穿过垂直计数线
        if (track_history_y_.find(track_id) != track_history_y_.end()) {
            float prev_y = track_history_y_[track_id];
            // 如果之前在线上方，现在在线下方，或者之前在线下方，现在在线上方
            if ((prev_y < line_y && y >= line_y) || (prev_y >= line_y && y < line_y)) {
                // 增加计数
                total_count_++;

                // 更新类别计数
                class_counts_[cls]++;

                std::cout << "目标 ID " << track_id << " 穿过垂直计数线 (位置: " << line_y
                          << "), 总计数: " << total_count_ << std::endl;
            }
        }

        // 检查是否穿过水平计数线
        if (track_history_x_.find(track_id) != track_history_x_.end()) {
            float prev_x = track_history_x_[track_id];
            // 如果之前在线左侧，现在在线右侧，或者之前在线右侧，现在在线左侧
            if ((prev_x < line_x && x >= line_x) || (prev_x >= line_x && x < line_x)) {
                // 增加计数
                total_count_++;

                // 更新类别计数
                class_counts_[cls]++;

                std::cout << "目标 ID " << track_id << " 穿过水平计数线 (位置: " << line_x
                          << "), 总计数: " << total_count_ << std::endl;
            }
        }

        // 更新跟踪历史
        track_history_y_[track_id] = y;
        track_history_x_[track_id] = x;
    }

    // 创建渲染信息
    Json::Value render_info = create_render_info(frame, tracks);

    // 创建一个副本用于绘制
    cv::Mat display_frame = frame.clone();

    // 设置结果
    result.task_type = "counting";
    result.frame_id = frame_count_;
    result.total_count = total_count_;
    result.class_counts = class_counts_;

    // 创建JSON对象存储扩展信息
    Json::Value ext_info_json;
    ext_info_json["line_y"] = line_y_;
    ext_info_json["line_x"] = line_x_;
    ext_info_json["frames_received"] = static_cast<int>(frames.size());
    ext_info_json["frames_requested"] = required_frames_;
    ext_info_json["history_frames_available"] = static_cast<int>(frames.size()) - 1;

    // 添加渲染信息
    ext_info_json["render_info"] = render_info;

    // 将JSON对象转换为字符串
    Json::FastWriter writer;
    result.ext_info = writer.write(ext_info_json);

    return true;
}

Json::Value CountingPlugin::create_render_info(const cv::Mat& frame,
                                             const std::vector<tracking::strack>& tracks) {
    Json::Value render_info;

    // 基本信息
    render_info["should_render"] = true;

    // 帧信息
    Json::Value frame_info;
    frame_info["frame_count"] = frame_count_;
    frame_info["object_count"] = total_count_;
    render_info["frame_info"] = frame_info;

    // 状态信息
    Json::Value status;
    status["total_objects"] = total_count_;
    status["status_text"] = "总计数: " + std::to_string(total_count_);
    status["is_error"] = false;

    // 添加类别计数信息
    std::string class_info;
    for (const auto& [cls, count] : class_counts_) {
        if (!class_info.empty()) {
            class_info += ", ";
        }
        class_info += cls + ": " + std::to_string(count);
    }

    if (!class_info.empty()) {
        status["class_info"] = class_info;
    }

    // 添加计数线位置信息
    status["count_line_info"] = "水平线: " + std::to_string(line_x_) +
                               ", 垂直线: " + std::to_string(line_y_);

    render_info["status"] = status;

    // 轨迹信息
    Json::Value tracks_info(Json::arrayValue);
    for (const auto& track : tracks) {
        Json::Value track_info;
        track_info["track_id"] = track.track_id;
        track_info["class"] = track.detect_class;

        Json::Value bbox(Json::arrayValue);
        bbox.append(static_cast<int>(track.tlwh.x));
        bbox.append(static_cast<int>(track.tlwh.y));
        bbox.append(static_cast<int>(track.tlwh.width));
        bbox.append(static_cast<int>(track.tlwh.height));
        track_info["bbox"] = bbox;

        tracks_info.append(track_info);
    }
    render_info["tracks"] = tracks_info;

    // 自定义元素
    Json::Value custom_elements(Json::arrayValue);

    // 计算实际的计数线位置
    int y_line_pos = static_cast<int>(line_y_ * frame.rows);
    int x_line_pos = static_cast<int>(line_x_ * frame.cols);

    std::cout << "渲染计数线 - 水平线位置: " << x_line_pos << " (比例: " << line_x_
              << "), 垂直线位置: " << y_line_pos << " (比例: " << line_y_ << ")" << std::endl;

    // 添加垂直计数线
    Json::Value v_line;
    v_line["type"] = "line";
    Json::Value v_points(Json::arrayValue);
    Json::Value v_start(Json::arrayValue);
    v_start.append(0);
    v_start.append(y_line_pos);
    Json::Value v_end(Json::arrayValue);
    v_end.append(frame.cols);
    v_end.append(y_line_pos);
    v_points.append(v_start);
    v_points.append(v_end);
    v_line["points"] = v_points;
    Json::Value v_color(Json::arrayValue);
    v_color.append(0);
    v_color.append(255);
    v_color.append(255);
    v_line["color"] = v_color;
    v_line["thickness"] = 2;
    custom_elements.append(v_line);

    // 添加水平计数线
    Json::Value h_line;
    h_line["type"] = "line";
    Json::Value h_points(Json::arrayValue);
    Json::Value h_start(Json::arrayValue);
    h_start.append(x_line_pos);
    h_start.append(0);
    Json::Value h_end(Json::arrayValue);
    h_end.append(x_line_pos);
    h_end.append(frame.rows);
    h_points.append(h_start);
    h_points.append(h_end);
    h_line["points"] = h_points;
    Json::Value h_color(Json::arrayValue);
    h_color.append(255);
    h_color.append(0);
    h_color.append(255);
    h_line["color"] = h_color;
    h_line["thickness"] = 2;
    custom_elements.append(h_line);

    render_info["custom_elements"] = custom_elements;

    return render_info;
}

void CountingPlugin::reset() {
    // 保存当前的计数线位置
    float saved_line_y = line_y_;
    float saved_line_x = line_x_;

    // 重置计数相关变量
    frame_count_ = 0;
    total_count_ = 0;
    class_counts_.clear();
    track_history_y_.clear();
    track_history_x_.clear();

    // 恢复计数线位置
    line_y_ = saved_line_y;
    line_x_ = saved_line_x;

    std::cout << "CountingPlugin::reset - 计数插件已重置，保留计数线位置: "
              << "line_y=" << line_y_ << ", "
              << "line_x=" << line_x_ << std::endl;
}

int CountingPlugin::get_required_frames() const {
    return required_frames_;
}

std::string CountingPlugin::get_type() const {
    return "counting";
}

std::string CountingPlugin::get_description() const {
    return "计算穿过计数线的目标数量";
}

std::string CountingPlugin::get_version() const {
    return "1.0.0";
}

std::string CountingPlugin::get_author() const {
    return "阿丘科技";
}



std::map<std::string, std::string> CountingPlugin::get_default_params() const {
    std::map<std::string, std::string> default_params;

    // 设置默认参数
    default_params["required_frames"] = "1";
    default_params["line_y"] = "0.5";
    default_params["line_x"] = "0.5";

    return default_params;
}

} // namespace plugins
} // namespace ai

// 导出函数实现
extern "C" {
    COUNTING_PLUGIN_API ai::plugins::TaskPlugin* CreateTaskPlugin() {
        return new ai::plugins::CountingPlugin();
    }

    COUNTING_PLUGIN_API void DestroyTaskPlugin(ai::plugins::TaskPlugin* plugin) {
        delete plugin;
    }

    COUNTING_PLUGIN_API int GetTaskPluginApiVersion() {
        return 1; // 与TaskPluginDllLoader::TASK_PLUGIN_API_VERSION保持一致
    }
}
