# 多路视频流处理示例

本示例演示了如何使用 AiVideoCore 库同时处理4路视频流，每路视频流绑定不同的项目配置，并在一个窗口中显示处理结果。

## 功能特点

1. 同时处理4路视频流（可以是视频文件或RTSP流）
2. 每路视频流可以绑定不同的项目配置
3. 使用多线程并行处理每路视频流
4. 在一个窗口中显示所有视频流的处理结果
5. 为每路视频流应用各自项目中配置的AI模型和插件
6. 实时显示每路视频流的性能指标（FPS、处理时间等）
7. 支持暂停/继续、重新加载单个视频流
8. 支持保存当前帧和视频流信息到文件
9. 提供键盘快捷键控制各种功能

## 编译

在项目根目录下执行以下命令：

```bash
mkdir build
cd build
cmake ..
cmake --build . --config Release
```

## 使用方法

```bash
./bin/multi_stream_example <项目文件路径1> [项目文件路径2] [项目文件路径3] [项目文件路径4]
```

例如：

```bash
./bin/multi_stream_example project1.aivp project2.aivp project3.aivp project4.aivp
```

如果提供的项目文件少于4个，程序会使用第一个项目文件填充剩余位置。例如：

```bash
./bin/multi_stream_example project1.aivp project2.aivp
```

这将使用 project1.aivp 处理第一个和第三个、第四个视频流，使用 project2.aivp 处理第二个视频流。

## 视频源配置

每个项目文件中必须包含有效的视频路径。程序会自动使用项目中配置的视频路径作为对应视频流的视频源。

## 键盘控制

程序提供了以下键盘快捷键：

| 按键 | 功能 |
|------|------|
| ESC | 退出程序 |
| H | 显示/隐藏帮助信息 |
| 1-4 | 暂停/继续对应视频流 |
| Shift+1-4 | 重新加载对应视频流 |
| S | 保存当前帧为图片 |
| J | 保存视频流信息到JSON文件 |
| R | 重置所有视频流（取消暂停） |
| F | 切换全屏模式 |

## 注意事项

1. 需要替换示例中的许可证 ID 和服务器地址为有效值
2. 项目文件必须是有效的 .aivp 文件
3. 项目文件中必须包含有效的视频路径和模型路径
4. 如果无法打开某个项目或视频源，将显示错误信息
5. 多路视频流处理会消耗较多系统资源，请确保您的系统有足够的计算能力
6. 重新加载视频流功能对于RTSP流特别有用，可以在连接断开后恢复

## 代码说明

示例代码展示了以下关键步骤：

1. 初始化 VisionFlow 环境
   ```cpp
   core::VideoProcessingCore::initialize_visionflow("your_license_id", "your_server_addr");
   ```

2. 从命令行参数获取项目文件路径
   ```cpp
   std::vector<std::string> projectPaths;
   for (int i = 1; i < argc && i <= 4; i++) {
       projectPaths.push_back(argv[i]);
   }
   ```

3. 为每路视频流打开不同的项目
   ```cpp
   streamInfo.project = projectManager.open_project(streamInfo.projectPath);
   ```

4. 为每路视频流创建处理核心
   ```cpp
   streamInfo.processor = std::make_shared<core::VideoProcessingCore>();
   ```

5. 加载模型并初始化运行时
   ```cpp
   streamInfo.processor->load_model(streamInfo.project->get_model_path());
   streamInfo.processor->initialize_runtime(streamInfo.project->get_input_node_id(), streamInfo.project->get_output_node_id());
   ```

6. 将项目配置导入到处理核心
   ```cpp
   projectManager.import_to_video_processing_core(streamInfo.project, streamInfo.processor);
   ```

7. 打开视频源
   ```cpp
   streamInfo.processor->open_video_file(streamInfo.videoUrl);
   ```

8. 创建多线程处理视频流
   ```cpp
   threads.emplace_back(process_stream, std::ref(streams[i]), std::ref(running),
                        std::ref(displayMutex), std::ref(displayFrame),
                        displayRois[i]);
   ```

9. 实时监控视频流性能
   ```cpp
   // 计算处理时间(毫秒)
   stream_info.processingTime = std::chrono::duration<double, std::milli>(endTime - startTime).count();

   // 更新FPS
   stream_info.fps = framesSinceLastFpsUpdate / timeSinceLastFpsUpdate;
   ```

10. 处理键盘事件
    ```cpp
    switch (key) {
        case 'h': // H键 - 显示/隐藏帮助
            showHelp = !showHelp;
            break;
        case '1': // 1-4键 - 暂停/继续对应视频流
            streams[streamIndex].isPaused = !streams[streamIndex].isPaused;
            break;
        // 更多键盘事件处理...
    }
    ```

11. 保存视频流信息到JSON
    ```cpp
    bool save_stream_info_to_json(const std::vector<StreamInfo>& streams, const std::string& filename) {
        Json::Value root;
        // 填充JSON数据...
        file << writer.write(root);
    }
    ```

12. 重新加载视频流
    ```cpp
    bool reload_stream(StreamInfo& stream_info) {
        stream_info.processor->close_video();
        // 重新打开视频源...
        return opened;
    }
    ```
