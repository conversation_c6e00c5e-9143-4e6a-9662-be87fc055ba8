@echo off
REM 检查当前 jsoncpp 链接方式的脚本

echo 检查 jsoncpp 链接方式...

REM 检查构建目录是否存在
if not exist "build" (
    echo 错误: 构建目录不存在，请先构建项目
    pause
    exit /b 1
)

echo.
echo === 检查 vcpkg 安装的 jsoncpp 文件 ===
if exist "build\vcpkg_installed\x64-windows\bin\jsoncpp.dll" (
    echo [动态] 找到 jsoncpp.dll: build\vcpkg_installed\x64-windows\bin\jsoncpp.dll
) else (
    echo [静态] 未找到 jsoncpp.dll（可能使用静态链接）
)

if exist "build\vcpkg_installed\x64-windows\lib\jsoncpp.lib" (
    echo [静态] 找到 jsoncpp.lib: build\vcpkg_installed\x64-windows\lib\jsoncpp.lib
)

if exist "build\vcpkg_installed\x64-windows-static\lib\jsoncpp.lib" (
    echo [静态] 找到静态三元组的 jsoncpp.lib: build\vcpkg_installed\x64-windows-static\lib\jsoncpp.lib
)

echo.
echo === 检查构建输出中的 jsoncpp.dll ===
set FOUND_DLL=0
for /r "build" %%f in (jsoncpp.dll) do (
    echo [动态] 找到: %%f
    set FOUND_DLL=1
)

if %FOUND_DLL%==0 (
    echo [静态] 构建输出中未找到 jsoncpp.dll（使用静态链接）
)

echo.
echo === 检查最终输出目录 ===
if exist "bin\release\jsoncpp.dll" (
    echo [动态] 最终输出包含 jsoncpp.dll: bin\release\jsoncpp.dll
) else (
    echo [静态] 最终输出不包含 jsoncpp.dll（使用静态链接）
)

echo.
echo === 检查可执行文件依赖（如果存在）===
if exist "bin\release\direct_project_example.exe" (
    echo 检查 direct_project_example.exe 的依赖:
    dumpbin /dependents "bin\release\direct_project_example.exe" 2>nul | findstr /i jsoncpp
    if %ERRORLEVEL%==0 (
        echo [动态] 可执行文件依赖 jsoncpp.dll
    ) else (
        echo [静态] 可执行文件不依赖 jsoncpp.dll
    )
) else (
    echo 未找到可执行文件，无法检查依赖关系
)

echo.
echo === 总结 ===
echo 根据上述检查结果，您可以判断当前使用的是静态链接还是动态链接。
echo 如果要切换链接方式，请使用:
echo   configure_static_jsoncpp.bat   (静态链接)
echo   configure_dynamic_jsoncpp.bat  (动态链接)

pause
