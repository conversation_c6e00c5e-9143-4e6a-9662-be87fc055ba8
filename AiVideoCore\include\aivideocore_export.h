#pragma once

// 定义导出宏，用于正确导出/导入符号
#if defined(_MSC_VER)
    #if defined(AIVIDEOCORE_SHARED)
        #if defined(AIVIDEOCORE_EXPORTS)
            #define AIVIDEOCORE_API __declspec(dllexport)
        #else
            #define AIVIDEOCORE_API __declspec(dllimport)
        #endif
    #else
        #define AIVIDEOCORE_API
    #endif
#else
    #if defined(AIVIDEOCORE_SHARED)
        #if defined(AIVIDEOCORE_EXPORTS)
            #define AIVIDEOCORE_API __attribute__((visibility("default")))
        #else
            #define AIVIDEOCORE_API
        #endif
    #else
        #define AIVIDEOCORE_API
    #endif
#endif
