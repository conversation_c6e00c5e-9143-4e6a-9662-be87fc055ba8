#include "ai/plugins/dll_plugin_loader.h"
#include <iostream>
#include <filesystem>
#ifdef _WIN32
#include <winsock2.h>
#else
#include <dlfcn.h> // For Linux dynamic library loading
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>
#endif

namespace ai {
namespace plugins {

// 定义插件API函数类型
typedef FrameProcessorPlugin* (*CreatePluginFunc)();
typedef void (*DestroyPluginFunc)(FrameProcessorPlugin*);
typedef int (*GetPluginApiVersionFunc)();

// 自定义删除器，用于在shared_ptr销毁时正确释放DLL插件
class PluginDeleter {
public:
#ifdef _WIN32
    PluginDeleter(HMODULE handle, DestroyPluginFunc destroy_func)
        : handle_(handle), destroy_func_(destroy_func) {}
#else
    PluginDeleter(void* handle, DestroyPluginFunc destroy_func)
        : handle_(handle), destroy_func_(destroy_func) {}
#endif

    void operator()(FrameProcessorPlugin* plugin) {
        if (plugin && destroy_func_) {
            destroy_func_(plugin);
        }
#ifdef _WIN32
        if (handle_) {
            FreeLibrary(handle_);
        }
#else
        if (handle_) {
            dlclose(handle_);
        }
#endif
    }

private:
#ifdef _WIN32
    HMODULE handle_;
#else
    void* handle_;
#endif
    DestroyPluginFunc destroy_func_;
};

std::shared_ptr<FrameProcessorPlugin> DllPluginLoader::load_plugin(const std::string& dll_path) {
#ifdef _WIN32
    std::wstring wide_dll_path = std::wstring(dll_path.begin(), dll_path.end());
    HMODULE handle = LoadLibrary(wide_dll_path.c_str());
    if (!handle) {
        error_message_ = get_windows_error_message();
        return nullptr;
    }
#else
    void* handle = dlopen(dll_path.c_str(), RTLD_LAZY);
    if (!handle) {
        error_message_ = get_linux_error_message();
        return nullptr;
    }
#endif

    // 检查是否是有效的插件
    if (!is_valid_plugin(handle)) {
#ifdef _WIN32
        FreeLibrary(handle);
#else
        dlclose(handle);
#endif
        return nullptr;
    }

    // 获取导出函数
#ifdef _WIN32
    CreatePluginFunc create_func = (CreatePluginFunc)GetProcAddress(handle, "CreatePlugin");
    DestroyPluginFunc destroy_func = (DestroyPluginFunc)GetProcAddress(handle, "DestroyPlugin");
    GetPluginApiVersionFunc version_func = (GetPluginApiVersionFunc)GetProcAddress(handle, "GetPluginApiVersion");
#else
    CreatePluginFunc create_func = (CreatePluginFunc)dlsym(handle, "CreatePlugin");
    DestroyPluginFunc destroy_func = (DestroyPluginFunc)dlsym(handle, "DestroyPlugin");
    GetPluginApiVersionFunc version_func = (GetPluginApiVersionFunc)dlsym(handle, "GetPluginApiVersion");
#endif

    if (!create_func || !destroy_func || !version_func) {
        error_message_ = "Invalid plugin: missing required export functions";
#ifdef _WIN32
        FreeLibrary(handle);
#else
        dlclose(handle);
#endif
        return nullptr;
    }

    // 检查API版本
    int api_version = version_func();
    if (api_version != PLUGIN_API_VERSION) {
        error_message_ = "Plugin API version mismatch";
#ifdef _WIN32
        FreeLibrary(handle);
#else
        dlclose(handle);
#endif
        return nullptr;
    }

    // 创建插件实例
    FrameProcessorPlugin* plugin = create_func();
    if (!plugin) {
        error_message_ = "Failed to create plugin instance";
#ifdef _WIN32
        FreeLibrary(handle);
#else
        dlclose(handle);
#endif
        return nullptr;
    }

    // 创建带自定义删除器的shared_ptr
    return std::shared_ptr<FrameProcessorPlugin>(plugin, PluginDeleter(handle, destroy_func));
}

std::vector<std::shared_ptr<FrameProcessorPlugin>> DllPluginLoader::load_plugins_from_directory(const std::string& directory) {
    std::vector<std::shared_ptr<FrameProcessorPlugin>> plugins;

    try {
        // 检查目录是否存在
        if (!std::filesystem::exists(directory) || !std::filesystem::is_directory(directory)) {
            error_message_ = "Directory does not exist or is not a directory: " + directory;
            return plugins;
        }

        // 遍历目录中的所有DLL文件
        for (const auto& entry : std::filesystem::directory_iterator(directory)) {
            if (entry.is_regular_file() && entry.path().extension() == ".dll") {
                std::string dll_path = entry.path().string();
                std::cout << "Loading plugin from: " << dll_path << std::endl;

                auto plugin = load_plugin(dll_path);
                if (plugin) {
                    std::cout << "Successfully loaded plugin: " << plugin->get_name() << std::endl;
                    plugins.push_back(plugin);
                } else {
                    std::cout << "Failed to load plugin: " << error_message_ << std::endl;
                }
            }
        }
    } catch (const std::exception& e) {
        error_message_ = "Exception while loading plugins: " + std::string(e.what());
        std::cout << error_message_ << std::endl;
    }

    return plugins;
}

#ifdef _WIN32
// Windows-specific implementation of is_valid_plugin
bool DllPluginLoader::is_valid_plugin(HMODULE handle) {
    if (!handle) {
        error_message_ = "Invalid DLL handle";
        return false;
    }

    // 检查是否包含必要的导出函数
    if (!GetProcAddress(handle, "CreatePlugin")) {
        error_message_ = "Missing CreatePlugin export function";
        return false;
    }

    if (!GetProcAddress(handle, "DestroyPlugin")) {
        error_message_ = "Missing DestroyPlugin export function";
        return false;
    }

    if (!GetProcAddress(handle, "GetPluginApiVersion")) {
        error_message_ = "Missing GetPluginApiVersion export function";
        return false;
    }

    return true;
}

std::string DllPluginLoader::get_windows_error_message() {
    DWORD error_code = GetLastError();
    if (error_code == 0) {
        return "No error";
    }

    LPSTR message_buffer = nullptr;
    size_t size = FormatMessageA(
        FORMAT_MESSAGE_ALLOCATE_BUFFER | FORMAT_MESSAGE_FROM_SYSTEM | FORMAT_MESSAGE_IGNORE_INSERTS,
        NULL, error_code, MAKELANGID(LANG_NEUTRAL, SUBLANG_DEFAULT), (LPSTR)&message_buffer, 0, NULL);

    std::string message(message_buffer, size);
    LocalFree(message_buffer);

    return message;
}
#endif

#ifndef _WIN32
// Linux-specific implementation of is_valid_plugin
bool DllPluginLoader::is_valid_plugin(void* handle) {
    if (!handle) {
        error_message_ = "Invalid SO handle";
        return false;
    }

    // Check for required symbols in the shared object
    if (!dlsym(handle, "CreatePlugin")) {
        error_message_ = "Missing CreatePlugin export function";
        return false;
    }

    if (!dlsym(handle, "DestroyPlugin")) {
        error_message_ = "Missing DestroyPlugin export function";
        return false;
    }

    if (!dlsym(handle, "GetPluginApiVersion")) {
        error_message_ = "Missing GetPluginApiVersion export function";
        return false;
    }

    return true;
}

std::string DllPluginLoader::get_linux_error_message() {
    const char* error = dlerror();
    return error ? std::string(error) : "No error";
}
#endif 

std::string DllPluginLoader::get_error_message() const {
    return error_message_;
}

bool DllPluginLoader::initialize_network() {
#ifdef _WIN32
    WSADATA wsa_data;
    int result = WSAStartup(MAKEWORD(2, 2), &wsa_data);
    if (result != 0) {
        error_message_ = "WSAStartup failed with error: " + std::to_string(result);
        return false;
    }
#else
    // Linux does not require explicit initialization for sockets
#endif
    return true;
}

void DllPluginLoader::shutdown_network() {
#ifdef _WIN32
    WSACleanup();
#else
    // Linux does not require explicit cleanup for sockets
#endif
}

} // namespace plugins
} // namespace ai
