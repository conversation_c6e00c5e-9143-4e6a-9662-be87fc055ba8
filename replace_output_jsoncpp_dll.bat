@echo off
REM 替换构建输出中的 jsoncpp.dll 的脚本
REM 使用方法: replace_output_jsoncpp_dll.bat "C:\path\to\your\jsoncpp.dll"

if "%~1"=="" (
    echo 错误: 请提供您的 jsoncpp.dll 文件路径
    echo 使用方法: %0 "C:\path\to\your\jsoncpp.dll"
    pause
    exit /b 1
)

set YOUR_JSONCPP_DLL=%~1

REM 检查您的 DLL 文件是否存在
if not exist "%YOUR_JSONCPP_DLL%" (
    echo 错误: 文件 "%YOUR_JSONCPP_DLL%" 不存在
    pause
    exit /b 1
)

echo 正在替换构建输出中的 jsoncpp.dll...

REM 查找所有构建输出中的 jsoncpp.dll 文件
set BUILD_LOCATIONS=build\AiVideoCore\Release build\examples\direct_project_example\Release build\examples\multi_stream_example\Release build\examples\project_video_example\Release build\release\Release

for %%L in (%BUILD_LOCATIONS%) do (
    if exist "%%L\jsoncpp.dll" (
        echo 备份 %%L\jsoncpp.dll...
        copy "%%L\jsoncpp.dll" "%%L\jsoncpp.dll.backup"
        
        echo 替换 %%L\jsoncpp.dll...
        copy "%YOUR_JSONCPP_DLL%" "%%L\jsoncpp.dll"
        if %ERRORLEVEL% neq 0 (
            echo 替换 %%L\jsoncpp.dll 失败
        ) else (
            echo 替换 %%L\jsoncpp.dll 成功
        )
    )
)

REM 如果 bin/release 目录中有 jsoncpp.dll，也替换它
if exist "bin\release\jsoncpp.dll" (
    echo 备份 bin\release\jsoncpp.dll...
    copy "bin\release\jsoncpp.dll" "bin\release\jsoncpp.dll.backup"
    
    echo 替换 bin\release\jsoncpp.dll...
    copy "%YOUR_JSONCPP_DLL%" "bin\release\jsoncpp.dll"
    if %ERRORLEVEL% neq 0 (
        echo 替换 bin\release\jsoncpp.dll 失败
    ) else (
        echo 替换 bin\release\jsoncpp.dll 成功
    )
)

echo.
echo 替换完成！您的本地 jsoncpp.dll 现在已经替换了所有构建输出中的版本。
echo.
echo 注意: 下次重新构建时，这些文件可能会被覆盖。
echo 如果需要永久替换，建议使用 replace_jsoncpp_dll.bat 脚本。

pause
