#pragma once

#ifdef _WIN32
#include <winsock2.h>
#include <ws2tcpip.h>
#pragma comment(lib, "Ws2_32.lib")
#else
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>
#include <fcntl.h>
#endif
#include <string>
#include <functional>
#include <memory>
#include <stdexcept>
#include <thread>
#include <atomic>
#include <vector>
#include <mutex>

namespace core {

class TcpConnection {
public:
    TcpConnection(int socket);
    ~TcpConnection();

    bool send(const std::string& message);
    bool is_connected() const { return is_connected_; }

private:
    int socket_;
    std::atomic<bool> is_connected_;
    std::mutex send_mutex_;
};

class TcpServer {
public:
    TcpServer(int port, std::function<void(std::shared_ptr<TcpConnection>)> on_new_connection);
    ~TcpServer();

    void stop();

private:
    void accept_thread_func();

    int listen_socket_;
    std::atomic<bool> running_;
    std::thread accept_thread_;
    std::function<void(std::shared_ptr<TcpConnection>)> on_new_connection_;
};

} // namespace core