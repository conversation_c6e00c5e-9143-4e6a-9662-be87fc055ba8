# 添加直接项目处理示例
add_executable(direct_project_example direct_project_example.cpp)

# 链接 AiVideoCore 库
target_link_libraries(direct_project_example PRIVATE AiVideoCore)

# 添加链接选项，使用 --wrap=pthread_yield
target_link_options(direct_project_example PRIVATE -Wl,--wrap=pthread_yield)

# 链接 Boost 库
find_package(boost_filesystem CONFIG REQUIRED)
target_link_libraries(direct_project_example PUBLIC Boost::filesystem)
find_package(boost_log CONFIG REQUIRED)
target_link_libraries(direct_project_example PUBLIC Boost::log)

# 安装示例
install(TARGETS direct_project_example
        RUNTIME DESTINATION release
        LIBRARY DESTINATION lib
        ARCHIVE DESTINATION lib)

# 确保插件目录存在
install(CODE "file(MAKE_DIRECTORY \${CMAKE_INSTALL_PREFIX}/release/plugins/task)")

# 复制依赖的 DLL 文件到输出目录（仅适用于 Windows）
if(WIN32)
    add_custom_command(TARGET direct_project_example POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
            $<TARGET_FILE:AiVideoCore>
            $<TARGET_FILE_DIR:direct_project_example>
    )
endif()
