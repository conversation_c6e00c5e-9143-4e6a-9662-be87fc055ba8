# jsoncpp 依赖配置选项总结

本项目现在支持多种 jsoncpp 依赖配置方式，您可以根据需要选择最适合的方案。

## 🎯 可用选项

### 1. 静态链接 jsoncpp（推荐）
**特点**: 不依赖 jsoncpp.dll，可执行文件自包含

**使用方法**:
```batch
configure_static_jsoncpp.bat
```

**优势**:
- ✅ 无需分发 jsoncpp.dll
- ✅ 部署简单
- ✅ 避免版本冲突
- ✅ 性能优化

**劣势**:
- ❌ 可执行文件较大
- ❌ 编译时间较长

### 2. 动态链接 jsoncpp（默认）
**特点**: 依赖 jsoncpp.dll，需要运行时提供

**使用方法**:
```batch
configure_dynamic_jsoncpp.bat
```

**优势**:
- ✅ 可执行文件较小
- ✅ 编译速度快
- ✅ 库可以独立更新

**劣势**:
- ❌ 需要分发 jsoncpp.dll
- ❌ 可能出现版本冲突

### 3. 使用本地 jsoncpp 库
**特点**: 使用您指定的本地 jsoncpp 版本

**使用方法**:
```batch
configure_local_jsoncpp.bat "C:\path\to\your\jsoncpp"
```

**优势**:
- ✅ 完全控制 jsoncpp 版本
- ✅ 可以使用自定义编译的版本
- ✅ 灵活性最高

**劣势**:
- ❌ 需要手动管理依赖
- ❌ 配置相对复杂

### 4. 直接替换 DLL 文件
**特点**: 保持现有配置，只替换 DLL 文件

**使用方法**:
```batch
replace_jsoncpp_dll.bat "C:\path\to\your\jsoncpp.dll"
```

**优势**:
- ✅ 最简单的替换方式
- ✅ 不需要重新配置
- ✅ 可以快速测试

**劣势**:
- ❌ 每次重新构建可能被覆盖
- ❌ 不是永久解决方案

## 🛠️ 实用工具脚本

| 脚本名称 | 功能 | 使用场景 |
|----------|------|----------|
| `configure_static_jsoncpp.bat` | 配置静态链接 | 想要自包含的可执行文件 |
| `configure_dynamic_jsoncpp.bat` | 配置动态链接 | 默认配置，需要 DLL |
| `configure_local_jsoncpp.bat` | 使用本地库 | 有自定义 jsoncpp 版本 |
| `replace_jsoncpp_dll.bat` | 替换 DLL 文件 | 快速测试不同 DLL |
| `replace_output_jsoncpp_dll.bat` | 替换输出 DLL | 只替换最终输出 |
| `restore_original_jsoncpp.bat` | 恢复原始版本 | 回退到 vcpkg 版本 |
| `check_jsoncpp_linking.bat` | 检查链接方式 | 验证当前配置 |
| `test_static_config.bat` | 测试静态配置 | 验证配置是否正确 |

## 📋 决策指南

### 选择静态链接，如果您：
- 希望简化部署
- 不介意较大的可执行文件
- 想要避免 DLL 依赖问题
- 追求最佳性能

### 选择动态链接，如果您：
- 希望较小的可执行文件
- 需要独立更新库
- 有多个程序共享同一库
- 开发阶段需要快速编译

### 选择本地库，如果您：
- 有特定版本要求
- 需要自定义编译选项
- 想要完全控制依赖
- 有兼容性问题需要解决

### 选择直接替换，如果您：
- 只是临时测试
- 不想修改构建配置
- 需要快速验证某个版本
- 在调试特定问题

## 🔍 验证方法

### 检查当前配置：
```batch
check_jsoncpp_linking.bat
```

### 验证静态链接：
- 构建输出中没有 jsoncpp.dll
- 可执行文件不依赖 jsoncpp.dll
- CMake 输出显示使用静态库

### 验证动态链接：
- 构建输出中包含 jsoncpp.dll
- 可执行文件依赖 jsoncpp.dll
- CMake 输出显示使用动态库

## 📝 推荐工作流程

1. **评估需求**: 根据上述指南选择合适的方案
2. **执行配置**: 运行相应的配置脚本
3. **验证结果**: 使用检查脚本确认配置
4. **构建测试**: 完整构建并测试功能
5. **文档记录**: 在项目文档中记录选择的方案

## ⚠️ 注意事项

- 不同链接方式可能需要不同的编译定义
- 静态链接需要确保许可证兼容性
- 混合链接方式可能导致运行时问题
- 建议在整个项目中保持一致的链接方式

选择最适合您项目需求的配置方案，并使用提供的工具脚本来简化配置过程！
