#include "utils/cv_utils.h"

#ifdef _WIN32
#include <windows.h>
#endif

#include "utils/string_utils.h"

namespace utils {

void putTextZH(cv::Mat& img, 
               const std::string& text, 
               cv::Point pos,
               double fontSize,
               cv::Scalar color,
               int thickness) {
    #ifdef _WIN32
    // Windows-specific implementation
    std::string displayText = utf8ToAnsi(text);
    int fontHeight = static_cast<int>(30 * fontSize);
    HDC hdc = CreateCompatibleDC(NULL);
    SetBkMode(hdc, TRANSPARENT);
    
    LOGFONTA font;
    memset(&font, 0, sizeof(font));
    font.lfHeight = -fontHeight;
    font.lfWeight = FW_NORMAL;
    strcpy_s(font.lfFaceName, "微软雅黑");
    HFONT hFont = CreateFontIndirectA(&font);
    HFONT hOldFont = (HFONT)SelectObject(hdc, hFont);

    SIZE size;
    GetTextExtentPoint32A(hdc, displayText.c_str(), static_cast<int>(displayText.length()), &size);

    BITMAPINFO bmi;
    ZeroMemory(&bmi, sizeof(BITMAPINFO));
    bmi.bmiHeader.biSize = sizeof(BITMAPINFOHEADER);
    bmi.bmiHeader.biWidth = size.cx;
    bmi.bmiHeader.biHeight = -size.cy;
    bmi.bmiHeader.biPlanes = 1;
    bmi.bmiHeader.biBitCount = 32;
    bmi.bmiHeader.biCompression = BI_RGB;

    void* bits;
    HBITMAP hBmp = CreateDIBSection(hdc, &bmi, DIB_RGB_COLORS, &bits, NULL, 0);
    HBITMAP hOldBmp = (HBITMAP)SelectObject(hdc, hBmp);

    SetTextColor(hdc, RGB(color[2], color[1], color[0]));
    TextOutA(hdc, 0, 0, displayText.c_str(), static_cast<int>(displayText.length()));

    cv::Mat textImg(size.cy, size.cx, CV_8UC4, bits);
    cv::Mat textMask;
    cv::cvtColor(textImg, textMask, cv::COLOR_BGRA2GRAY);

    for(int i = 0; i < thickness; i++) {
        cv::Mat mask;
        cv::threshold(textMask, mask, 0, 255, cv::THRESH_BINARY);
        
        cv::Point offset(pos.x, pos.y - size.cy);
        for(int y = 0; y < textImg.rows; y++) {
            for(int x = 0; x < textImg.cols; x++) {
                if(mask.at<uchar>(y, x) > 0) {
                    cv::Point p(offset.x + x, offset.y + y);
                    if(p.x >= 0 && p.x < img.cols && p.y >= 0 && p.y < img.rows) {
                        img.at<cv::Vec3b>(p) = cv::Vec3b(color[0], color[1], color[2]);
                    }
                }
            }
        }
        pos.x += 1;
    }

    SelectObject(hdc, hOldBmp);
    DeleteObject(hBmp);
    SelectObject(hdc, hOldFont);
    DeleteObject(hFont);
    DeleteDC(hdc);
    #else
    // Linux stub implementation
    cv::putText(img, text, pos, cv::FONT_HERSHEY_SIMPLEX, fontSize, color, thickness);
    #endif
}

} // namespace utils
