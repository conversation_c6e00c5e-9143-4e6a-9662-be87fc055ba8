#pragma once

#include <string>
#include <memory>
#include <vector>
#ifdef _WIN32
#include <Windows.h>
#include <winsock2.h> // For Windows network communication
#pragma comment(lib, "ws2_32.lib")
#else
#include <dlfcn.h> // For Linux dynamic library loading
#include <sys/socket.h> // For Linux network communication
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>
#endif
#include "ai/plugins/frame_processor_plugin.h"

namespace ai {
namespace plugins {

/**
 * @brief DLL插件加载器，用于加载DLL插件
 */
class AIVIDEOCORE_API DllPluginLoader {
public:
    /**
     * @brief 构造函数
     */
    DllPluginLoader()=default;

    /**
     * @brief 析构函数
     */
    ~DllPluginLoader()=default;

    /**
     * @brief 加载DLL插件
     * @param dll_path DLL文件路径
     * @return 插件指针，如果加载失败则返回nullptr
     */
    std::shared_ptr<FrameProcessorPlugin> load_plugin(const std::string& dll_path);

    /**
     * @brief 扫描目录加载所有DLL插件
     * @param directory 目录路径
     * @return 加载的插件列表
     */
    std::vector<std::shared_ptr<FrameProcessorPlugin>> load_plugins_from_directory(const std::string& directory);

    /**
     * @brief 获取错误信息
     * @return 错误信息
     */
    std::string get_error_message() const;

    /**
     * @brief 初始化网络通信（跨平台）
     * @return 是否初始化成功
     */
    bool initialize_network();

    /**
     * @brief 关闭网络通信（跨平台）
     */
    void shutdown_network();

private:
#ifdef _WIN32
    /**
     * @brief 检查DLL是否是有效的插件
     * @param handle DLL句柄
     * @return 是否是有效的插件
     */
    bool is_valid_plugin(HMODULE handle);

    /**
     * @brief 获取Windows错误信息
     * @return 错误信息
     */
    std::string get_windows_error_message();
#else
    /**
     * @brief 检查SO是否是有效的插件
     * @param handle SO句柄
     * @return 是否是有效的插件
     */
    bool is_valid_plugin(void* handle);

    /**
     * @brief 获取Linux错误信息
     * @return 错误信息
     */
    std::string get_linux_error_message();
#endif

    std::string error_message_; ///< 错误信息
    static constexpr int PLUGIN_API_VERSION = 1; ///< 插件API版本
};

} // namespace plugins
} // namespace ai
