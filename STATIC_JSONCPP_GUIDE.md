# jsoncpp 静态链接配置

项目已配置为默认使用静态链接的 jsoncpp，不再依赖 jsoncpp.dll。

## 🎯 使用方法

### VSCode CMake 插件
1. 打开 VSCode
2. 使用 CMake 插件配置项目
3. 选择构建类型（Release/Debug）
4. 构建项目

### 命令行构建
```batch
mkdir build
cd build
cmake .. -DCMAKE_BUILD_TYPE=Release
cmake --build . --config Release
```

## 🔧 配置说明

### 主要更改
1. **vcpkg.json**: 设置默认使用 `x64-windows-static` 三元组
2. **AiVideoCore/CMakeLists.txt**: 添加 `JSON_DLL=0` 编译定义
3. **主 CMakeLists.txt**: 排除 jsoncpp.dll 的复制

## ✅ 优势

- **无 DLL 依赖**: 不需要分发 jsoncpp.dll
- **部署简单**: 可执行文件自包含
- **避免版本冲突**: 不会与系统中其他 jsoncpp 版本冲突

## 🔍 验证

构建完成后，检查 `bin/release` 目录应该没有 jsoncpp.dll 文件。
