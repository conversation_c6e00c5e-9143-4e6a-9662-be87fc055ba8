# jsoncpp 静态链接配置指南

本指南说明如何将项目配置为使用静态链接的 jsoncpp，避免对 jsoncpp.dll 的依赖。

## 🎯 快速开始

### 方法一：使用配置脚本（推荐）

#### 静态链接配置：
```batch
configure_static_jsoncpp.bat
```

#### 动态链接配置：
```batch
configure_dynamic_jsoncpp.bat
```

### 方法二：手动配置

#### 静态链接：
```batch
mkdir build
cd build
cmake .. -DCMAKE_BUILD_TYPE=Release -DVCPKG_TARGET_TRIPLET=x64-windows-static -DUSE_STATIC_JSONCPP=ON
cmake --build . --config Release
```

#### 动态链接：
```batch
mkdir build
cd build
cmake .. -DCMAKE_BUILD_TYPE=Release -DVCPKG_TARGET_TRIPLET=x64-windows -DUSE_STATIC_JSONCPP=OFF
cmake --build . --config Release
```

## 📋 配置选项

项目现在支持以下 CMake 选项：

- `USE_STATIC_JSONCPP=ON`：使用静态链接的 jsoncpp（默认）
- `USE_STATIC_JSONCPP=OFF`：使用动态链接的 jsoncpp

## 🔧 技术细节

### CMake 配置更改

1. **AiVideoCore/CMakeLists.txt**：
   - 添加了 `USE_STATIC_JSONCPP` 选项
   - 智能检测并选择合适的 jsoncpp 目标
   - 自动设置正确的编译定义

2. **主 CMakeLists.txt**：
   - 根据静态链接设置决定是否排除 jsoncpp.dll
   - 避免不必要的 DLL 复制

### vcpkg 三元组

- **x64-windows-static**：所有依赖都使用静态链接
- **x64-windows**：使用动态链接（默认）

## ✅ 静态链接的优势

1. **无 DLL 依赖**：不需要分发 jsoncpp.dll
2. **部署简单**：可执行文件自包含
3. **版本一致性**：避免 DLL 版本冲突
4. **性能优化**：编译器可以进行更好的优化

## ⚠️ 静态链接的注意事项

1. **文件大小**：可执行文件会更大
2. **编译时间**：可能需要更长的编译时间
3. **内存使用**：如果多个程序使用相同库，内存使用可能增加
4. **许可证**：确保静态链接符合 jsoncpp 的许可证要求

## 🔍 验证配置

### 检查是否使用静态链接

构建完成后，您可以通过以下方式验证：

#### Windows：
```batch
# 检查可执行文件的依赖
dumpbin /dependents bin\release\your_program.exe | findstr jsoncpp
```

如果使用静态链接，应该看不到 jsoncpp.dll 的依赖。

#### Linux：
```bash
# 检查共享库依赖
ldd bin/release/your_program | grep jsoncpp
```

### 检查构建输出

在 CMake 配置阶段，您应该看到类似的输出：

```
-- Configuring jsoncpp for static linking...
-- Using static jsoncpp library: JsonCpp::JsonCpp_static
-- Excluding jsoncpp.dll (using static linking)
```

## 🛠️ 故障排除

### 常见问题

1. **找不到静态库**：
   ```
   Static jsoncpp not found, falling back to dynamic linking
   ```
   **解决方案**：确保使用 `x64-windows-static` 三元组

2. **链接错误**：
   ```
   unresolved external symbol
   ```
   **解决方案**：检查编译定义，确保 `JSON_DLL=0`

3. **运行时错误**：
   ```
   The procedure entry point could not be located
   ```
   **解决方案**：确保没有混合静态和动态链接

### 调试技巧

1. **查看 CMake 变量**：
   ```cmake
   message(STATUS "jsoncpp_DIR: ${jsoncpp_DIR}")
   message(STATUS "VCPKG_TARGET_TRIPLET: ${VCPKG_TARGET_TRIPLET}")
   ```

2. **检查目标属性**：
   ```cmake
   get_target_property(JSONCPP_TYPE JsonCpp::JsonCpp TYPE)
   message(STATUS "JsonCpp target type: ${JSONCPP_TYPE}")
   ```

## 📝 最佳实践

1. **一致性**：在整个项目中保持一致的链接方式
2. **文档化**：在项目文档中明确说明链接方式
3. **测试**：在不同环境中测试静态链接的可执行文件
4. **备份**：保留动态链接的配置作为备选方案

## 🔄 切换链接方式

### 从动态切换到静态：
```batch
configure_static_jsoncpp.bat
```

### 从静态切换到动态：
```batch
configure_dynamic_jsoncpp.bat
```

## 📊 性能对比

| 特性 | 静态链接 | 动态链接 |
|------|----------|----------|
| 文件大小 | 较大 | 较小 |
| 启动速度 | 较快 | 较慢 |
| 内存使用 | 可能较高 | 较低 |
| 部署复杂度 | 简单 | 复杂 |
| 更新灵活性 | 低 | 高 |

根据您的具体需求选择合适的链接方式。
