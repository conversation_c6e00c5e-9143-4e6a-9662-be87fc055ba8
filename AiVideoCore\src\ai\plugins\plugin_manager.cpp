#include "ai/plugins/plugin_manager.h"

#include <filesystem>
#include "utils/settings_manager.h"

#include <iostream>

namespace ai {
namespace plugins {

PluginManager::PluginManager(const std::string& plugin_path) : plugin_path_(plugin_path) {
}

PluginManager::~PluginManager() {
    // 清理插件资源
    plugins_.clear();
}

bool PluginManager::register_plugin(std::shared_ptr<TaskPlugin> plugin) {
    if (!plugin) {
        return false;
    }

    const std::string& name = plugin->get_name();
    if (plugins_.find(name) != plugins_.end()) {
        // 插件已存在
        return false;
    }

    plugins_[name] = plugin;

    // 初始化插件，但默认设置为禁用状态
    if (!plugin->initialize()) {
        plugins_.erase(name);
        return false;
    }

    // 默认禁用所有插件
    plugin->set_enabled(false);

    // 加载插件的保存状态
    load_plugin_state(plugin);

    return true;
}

void PluginManager::load_plugin_state(std::shared_ptr<TaskPlugin> plugin) {
    if (!plugin) return;

    auto& settings = utils::SettingsManager::get_instance();
    settings.beginGroup("Plugins");
    // 如果配置文件中没有该插件的状态记录，则保持禁用状态
    bool enabled = settings.value(plugin->get_name(), utils::SettingsValue(false)).toBool();
    plugin->set_enabled(enabled);
    settings.endGroup();
}

void PluginManager::save_plugin_state(const std::string& name, bool enabled) {
    auto& settings = utils::SettingsManager::get_instance();
    settings.beginGroup("Plugins");
    settings.setValue(name, utils::SettingsValue(enabled));
    settings.endGroup();
}

bool PluginManager::unregister_plugin(const std::string& name) {
    auto it = plugins_.find(name);
    if (it == plugins_.end()) {
        // 插件不存在
        return false;
    }

    plugins_.erase(it);
    return true;
}

std::shared_ptr<TaskPlugin> PluginManager::get_plugin(const std::string& name) {
    auto it = plugins_.find(name);
    if (it == plugins_.end()) {
        return nullptr;
    }

    return it->second;
}

std::vector<std::shared_ptr<TaskPlugin>> PluginManager::get_all_plugins() {
    std::vector<std::shared_ptr<TaskPlugin>> result;
    for (const auto& pair : plugins_) {
        result.push_back(pair.second);
    }

    return result;
}

std::vector<std::shared_ptr<TaskPlugin>> PluginManager::get_plugins_by_type(const std::string& type) {
    std::vector<std::shared_ptr<TaskPlugin>> result;
    for (const auto& pair : plugins_) {
        if (pair.second->get_type() == type) {
            result.push_back(pair.second);
        }
    }

    return result;
}

bool PluginManager::enable_plugin(const std::string& name) {
    auto plugin = get_plugin(name);
    if (!plugin) {
        return false;
    }

    plugin->set_enabled(true);
    save_plugin_state(name, true);
    return true;
}

bool PluginManager::disable_plugin(const std::string& name) {
    auto plugin = get_plugin(name);
    if (!plugin) {
        return false;
    }

    plugin->set_enabled(false);
    save_plugin_state(name, false);
    return true;
}

bool PluginManager::set_plugin_enabled(const std::string& name, bool enabled) {
    auto plugin = get_plugin(name);
    if (!plugin) {
        return false;
    }

    plugin->set_enabled(enabled);
    return true;
}

void PluginManager::disable_all_plugins() {
    for (const auto& pair : plugins_) {
        pair.second->set_enabled(false);
    }
}

void PluginManager::process_all_plugins(cv::Mat& frame,
                                      const std::vector<tracking::strack>& tracks,
                                      FrameResult& result) {
    // 调用多帧接口
    std::vector<cv::Mat> frames = {frame};
    std::vector<std::vector<tracking::strack>> tracks_list = {tracks};
    process_all_plugins_batch(frames, tracks_list, result);
}

void PluginManager::process_all_plugins_batch(const std::vector<cv::Mat>& frames,
                                            const std::vector<std::vector<tracking::strack>>& tracks_list,
                                            FrameResult& result) {
    // 初始化结果
    result.task_type = "detection";  // 默认任务类型

    // 处理所有启用的插件
    for (const auto& pair : plugins_) {
        auto plugin = pair.second;
        if (plugin->is_enabled()) {
            // 获取插件需要的帧数
            int required_frames = plugin->get_required_frames();


            // 限制处理帧数，避免处理过多帧
            size_t frames_to_process = std::min(frames.size(), static_cast<size_t>(required_frames));
            bool pass_all_tracks_one_frame = false;
            if (frames_to_process == 0) {
                pass_all_tracks_one_frame = true;
            }

            // 如果插件只需要当前帧，或者帧数不足，则直接处理
            if (frames_to_process == frames.size()) {
                plugin->process_batch(frames, tracks_list, result);
            } else {
                if(pass_all_tracks_one_frame) {
                    plugin->process_batch({frames[0]}, tracks_list, result);
                }else{
                    // 创建子帧列表和子跟踪结果列表
                    std::vector<cv::Mat> sub_frames(frames.begin(), frames.begin() + frames_to_process);
                    std::vector<std::vector<tracking::strack>> sub_tracks_list(tracks_list.begin(), tracks_list.begin() + frames_to_process);

                    // 处理子帧列表
                    plugin->process_batch(sub_frames, sub_tracks_list, result);
                }
            }
        }
    }
}

void PluginManager::reset_all_plugins() {
    for (const auto& pair : plugins_) {
        pair.second->reset();
    }
}

void PluginManager::set_plugin_path(const std::string& path) {
    plugin_path_ = path;
}

std::string PluginManager::get_plugin_path() const {
    return plugin_path_;
}

bool PluginManager::load_plugin(const std::string& plugin_file) {
    std::string extension = std::filesystem::path(plugin_file).extension().string();
    bool success = false;

    // 根据文件扩展名选择加载方式
    if (extension == ".dll") {
        // 加载DLL插件
        success = load_dll_plugin(plugin_file);
    } else {
        // 其他类型的插件加载方式
        // 目前未实现，返回失败
        success = false;
    }

    // 调用回调函数通知加载结果
    if (plugin_load_callback_) {
        plugin_load_callback_(plugin_file, success);
    }

    return success;
}

int PluginManager::load_plugins_from_directory(const std::string& directory) {
    int count = 0;

    try {
        // 检查目录是否存在
        if (!std::filesystem::exists(directory)) {
            std::cerr << "Plugin directory does not exist: " << directory << std::endl;
            return 0;
        }

        // 加载DLL插件
        int dll_count = load_dll_plugins_from_directory(directory);
        count += dll_count;

        // 加载其他类型的插件
        for (const auto& entry : std::filesystem::directory_iterator(directory)) {
            if (entry.is_regular_file()) {
                std::string extension = entry.path().extension().string();
                // 过滤掉DLL插件，因为已经在上面加载了
                if (extension != ".dll" && (extension == ".so" || extension == ".plugin")) {
                    if (load_plugin(entry.path().string())) {
                        count++;
                    }
                }
            }
        }
    } catch (const std::exception& e) {
        std::cerr << "Error loading plugins: " << e.what() << std::endl;
    }

    return count;
}

void PluginManager::set_plugin_load_callback(std::function<void(const std::string&, bool)> callback) {
    plugin_load_callback_ = callback;
}

bool PluginManager::load_dll_plugin(const std::string& dll_path) {
    // 使用DLL插件加载器加载插件
    auto plugin = dll_plugin_loader_.load_plugin(dll_path);
    if (!plugin) {
        std::cerr << "Failed to load DLL plugin: " << dll_path << ", error: "
                  << dll_plugin_loader_.get_error_message() << std::endl;
        return false;
    }

    // 注册插件
    if (!register_plugin(plugin)) {
        std::cerr << "Failed to register DLL plugin: " << plugin->get_name() << std::endl;
        return false;
    }

    std::cout << "Successfully loaded and registered DLL plugin: " << plugin->get_name() << std::endl;
    return true;
}

int PluginManager::load_dll_plugins_from_directory(const std::string& directory) {
    int loaded_count = 0;

    // 从目录加载所有DLL插件
    auto plugins = dll_plugin_loader_.load_plugins_from_directory(directory);

    // 注册加载的插件
    for (auto& plugin : plugins) {
        if (register_plugin(plugin)) {
            std::cout << "Registered DLL plugin: " << plugin->get_name() << std::endl;
            loaded_count++;
        } else {
            std::cout << "Failed to register DLL plugin: " << plugin->get_name() << std::endl;
        }
    }

    return loaded_count;
}

} // namespace plugins
} // namespace ai

