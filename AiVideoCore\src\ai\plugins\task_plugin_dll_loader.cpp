#include "ai/plugins/task_plugin_dll_loader.h"
#include <iostream>
#include <filesystem>

#ifdef _WIN32
#include <Windows.h>
#else
#include <dlfcn.h> // For Linux dynamic library handling
#endif

namespace ai {
namespace plugins {

// 定义任务插件API函数类型
typedef TaskPlugin* (*CreateTaskPluginFunc)();
typedef void (*DestroyTaskPluginFunc)(TaskPlugin*);
typedef int (*GetTaskPluginApiVersionFunc)();

// 自定义删除器，用于在shared_ptr销毁时正确释放DLL插件
class TaskPluginDeleter {
public:
#ifdef _WIN32
    TaskPluginDeleter(HMODULE handle, DestroyTaskPluginFunc destroy_func)
        : handle_(handle), destroy_func_(destroy_func) {}
#else
    TaskPluginDeleter(void* handle, DestroyTaskPluginFunc destroy_func)
        : handle_(handle), destroy_func_(destroy_func) {}
#endif

    void operator()(TaskPlugin* plugin) {
        if (plugin && destroy_func_) {
            destroy_func_(plugin);
        }
#ifdef _WIN32
        if (handle_) {
            FreeLibrary(handle_);
        }
#else
        if (handle_) {
            dlclose(handle_);
        }
#endif
    }

private:
#ifdef _WIN32
    HMODULE handle_;
#else
    void* handle_;
#endif
    DestroyTaskPluginFunc destroy_func_;
};

TaskPluginDllLoader::TaskPluginDllLoader() {
}

TaskPluginDllLoader::~TaskPluginDllLoader() {
}

std::shared_ptr<TaskPlugin> TaskPluginDllLoader::load_plugin(const std::string& dll_path) {
    error_message_.clear();

#ifdef _WIN32
    HMODULE handle = LoadLibraryA(dll_path.c_str());
    if (!handle) {
        error_message_ = "Failed to load DLL: " + dll_path + ", error: " + get_windows_error_message();
        return nullptr;
    }
#else
    void* handle = dlopen(dll_path.c_str(), RTLD_LAZY);
    if (!handle) {
        error_message_ = "Failed to load SO: " + dll_path + ", error: " + get_linux_error_message();
        return nullptr;
    }
#endif

    if (!is_valid_plugin(handle)) {
#ifdef _WIN32
        FreeLibrary(handle);
#else
        dlclose(handle);
#endif
        return nullptr;
    }

#ifdef _WIN32
    CreateTaskPluginFunc create_func = (CreateTaskPluginFunc)GetProcAddress(handle, "CreateTaskPlugin");
    DestroyTaskPluginFunc destroy_func = (DestroyTaskPluginFunc)GetProcAddress(handle, "DestroyTaskPlugin");
    GetTaskPluginApiVersionFunc version_func = (GetTaskPluginApiVersionFunc)GetProcAddress(handle, "GetTaskPluginApiVersion");
#else
    CreateTaskPluginFunc create_func = (CreateTaskPluginFunc)dlsym(handle, "CreateTaskPlugin");
    DestroyTaskPluginFunc destroy_func = (DestroyTaskPluginFunc)dlsym(handle, "DestroyTaskPlugin");
    GetTaskPluginApiVersionFunc version_func = (GetTaskPluginApiVersionFunc)dlsym(handle, "GetTaskPluginApiVersion");
#endif

    if (!create_func || !destroy_func || !version_func) {
        error_message_ = "Invalid plugin: missing required export functions";
#ifdef _WIN32
        FreeLibrary(handle);
#else
        dlclose(handle);
#endif
        return nullptr;
    }

    int api_version = version_func();
    if (api_version != TASK_PLUGIN_API_VERSION) {
        error_message_ = "Plugin API version mismatch";
#ifdef _WIN32
        FreeLibrary(handle);
#else
        dlclose(handle);
#endif
        return nullptr;
    }

    TaskPlugin* plugin = create_func();
    if (!plugin) {
        error_message_ = "Failed to create plugin instance";
#ifdef _WIN32
        FreeLibrary(handle);
#else
        dlclose(handle);
#endif
        return nullptr;
    }

    return std::shared_ptr<TaskPlugin>(plugin, TaskPluginDeleter(handle, destroy_func));
}

std::vector<std::shared_ptr<TaskPlugin>> TaskPluginDllLoader::load_plugins_from_directory(const std::string& directory) {
    std::vector<std::shared_ptr<TaskPlugin>> plugins;

    try {
        // 检查目录是否存在
        if (!std::filesystem::exists(directory) || !std::filesystem::is_directory(directory)) {
            error_message_ = "Directory does not exist or is not a directory: " + directory;
            return plugins;
        }

        // 遍历目录中的所有DLL文件
        for (const auto& entry : std::filesystem::directory_iterator(directory)) {
            if (entry.is_regular_file() && entry.path().extension() == ".dll") {
                std::string dll_path = entry.path().string();
                std::cout << "Loading task plugin from: " << dll_path << std::endl;

                auto plugin = load_plugin(dll_path);
                if (plugin) {
                    std::cout << "Successfully loaded task plugin: " << plugin->get_name() << std::endl;
                    plugins.push_back(plugin);
                } else {
                    std::cout << "Failed to load task plugin: " << error_message_ << std::endl;
                }
            }
        }
    } catch (const std::exception& e) {
        error_message_ = "Exception while loading plugins: " + std::string(e.what());
        std::cout << error_message_ << std::endl;
    }

    return plugins;
}

#ifndef _WIN32
bool TaskPluginDllLoader::is_valid_plugin(void* handle) {
    if (!handle) {
        error_message_ = "Invalid SO handle";
        return false;
    }

    if (!dlsym(handle, "CreateTaskPlugin")) {
        error_message_ = "Missing CreateTaskPlugin export function";
        return false;
    }

    if (!dlsym(handle, "DestroyTaskPlugin")) {
        error_message_ = "Missing DestroyTaskPlugin export function";
        return false;
    }

    if (!dlsym(handle, "GetTaskPluginApiVersion")) {
        error_message_ = "Missing GetTaskPluginApiVersion export function";
        return false;
    }

    return true;
}

std::string TaskPluginDllLoader::get_linux_error_message() {
    const char* error = dlerror();
    return error ? std::string(error) : "No error";
}
#endif

#ifdef _WIN32
bool TaskPluginDllLoader::is_valid_plugin(HMODULE handle) {
    if (!handle) {
        error_message_ = "Invalid DLL handle";
        return false;
    }

    if (!GetProcAddress(handle, "CreateTaskPlugin")) {
        error_message_ = "Missing CreateTaskPlugin export function";
        return false;
    }

    if (!GetProcAddress(handle, "DestroyTaskPlugin")) {
        error_message_ = "Missing DestroyTaskPlugin export function";
        return false;
    }

    if (!GetProcAddress(handle, "GetTaskPluginApiVersion")) {
        error_message_ = "Missing GetTaskPluginApiVersion export function";
        return false;
    }

    return true;
}

std::string TaskPluginDllLoader::get_windows_error_message() {
    DWORD error_code = GetLastError();
    if (error_code == 0) {
        return "No error";
    }

    LPSTR message_buffer = nullptr;
    size_t size = FormatMessageA(
        FORMAT_MESSAGE_ALLOCATE_BUFFER | FORMAT_MESSAGE_FROM_SYSTEM | FORMAT_MESSAGE_IGNORE_INSERTS,
        NULL, error_code, MAKELANGID(LANG_NEUTRAL, SUBLANG_DEFAULT), (LPSTR)&message_buffer, 0, NULL);

    std::string message(message_buffer, size);
    LocalFree(message_buffer);

    return message;
}
#endif

std::string TaskPluginDllLoader::get_error_message() const {
    return error_message_;
}

} // namespace plugins
} // namespace ai
