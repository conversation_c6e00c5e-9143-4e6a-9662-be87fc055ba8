# jsoncpp.dll 替换指南

本指南说明如何直接替换 vcpkg 下载的 jsoncpp.dll 文件为您的本地版本。

## 🚀 快速开始

### 方法一：替换 vcpkg 源文件（推荐）

这种方法会替换 vcpkg 安装目录中的 jsoncpp.dll，确保每次构建都使用您的版本：

```batch
replace_jsoncpp_dll.bat "C:\path\to\your\jsoncpp.dll"
```

### 方法二：仅替换构建输出

如果您只想替换当前构建输出中的 DLL：

```batch
replace_output_jsoncpp_dll.bat "C:\path\to\your\jsoncpp.dll"
```

## 📍 jsoncpp.dll 位置

在您的项目中，vcpkg 的 jsoncpp.dll 位于以下位置：

### vcpkg 安装目录：
- **Release 版本**: `build\vcpkg_installed\x64-windows\bin\jsoncpp.dll`
- **Debug 版本**: `build\vcpkg_installed\x64-windows\debug\bin\jsoncpp.dll`

### 构建输出目录：
- `build\AiVideoCore\Release\jsoncpp.dll`
- `build\examples\direct_project_example\Release\jsoncpp.dll`
- `build\examples\multi_stream_example\Release\jsoncpp.dll`
- `build\examples\project_video_example\Release\jsoncpp.dll`
- `build\release\Release\jsoncpp.dll`

## 🔄 操作步骤

### 1. 准备您的 jsoncpp.dll
确保您有一个兼容的 jsoncpp.dll 文件，最好是：
- 与项目使用相同的编译器编译（MSVC）
- 相同的架构（x64）
- 兼容的版本

### 2. 执行替换
```batch
# 替换 vcpkg 源文件（推荐）
replace_jsoncpp_dll.bat "C:\your\path\jsoncpp.dll"

# 或者只替换构建输出
replace_output_jsoncpp_dll.bat "C:\your\path\jsoncpp.dll"
```

### 3. 重新构建项目
```batch
cd build
cmake --build . --config Release
```

## 🔙 恢复原始版本

如果需要恢复到 vcpkg 的原始版本：

```batch
restore_original_jsoncpp.bat
```

然后重新构建项目。

## ⚠️ 注意事项

### 兼容性
- 确保您的 jsoncpp.dll 与项目的其他组件兼容
- 版本差异可能导致 API 不兼容问题
- 建议使用相同或兼容的 jsoncpp 版本

### 备份
- 脚本会自动备份原始文件（.backup 扩展名）
- 建议在替换前手动备份重要文件

### 构建行为
- **方法一**：每次构建都会使用您的版本
- **方法二**：只影响当前构建输出，重新构建时会被覆盖

## 🔍 验证替换

替换后，您可以通过以下方式验证：

1. **检查文件时间戳**：
   ```batch
   dir build\vcpkg_installed\x64-windows\bin\jsoncpp.dll
   ```

2. **检查文件大小**：
   比较替换前后的文件大小

3. **运行程序**：
   运行您的程序，确保 jsoncpp 功能正常工作

## 🛠️ 故障排除

### 常见问题

1. **权限错误**：
   - 以管理员身份运行脚本
   - 确保文件没有被其他程序占用

2. **文件不存在**：
   - 确保项目已经构建过至少一次
   - 检查 vcpkg 是否正确安装了 jsoncpp

3. **运行时错误**：
   - 检查 DLL 依赖关系
   - 确保 DLL 架构匹配（x64）
   - 验证编译器兼容性

### 调试技巧

- 使用 `dumpbin /dependents jsoncpp.dll` 查看 DLL 依赖
- 使用 Process Monitor 监控文件访问
- 检查 Windows 事件日志中的错误信息

## 📝 总结

直接替换 jsoncpp.dll 是一个简单有效的方法，特别适合：
- 快速测试不同版本的 jsoncpp
- 使用自定义编译的 jsoncpp 版本
- 临时解决兼容性问题

对于长期使用，建议考虑使用 CMake 配置方法（参见 `LOCAL_JSONCPP_SETUP.md`）。
