#include "core/video_result_storage_server.h"

#include <chrono>
#include <filesystem>
#include <iomanip>
#include <iostream>
#include <sstream>
#include <thread>
#include "utils/log_manager.h"

#include "core/protocols/result_protocol.h"
#include "core/protocols/tcp_result_protocol.h"
#include "core/protocols/modbus_result_protocol.h"
#include "core/protocols/mqtt_result_protocol.h"

namespace core {

// VideoResultStorageServer实现
VideoResultStorageServer::VideoResultStorageServer(const std::string& storage_path,
                                                 StorageMode mode,
                                                 int flush_interval_ms)
    : storage_path_(storage_path),
      storage_mode_(mode),
      flush_interval_ms_(flush_interval_ms),
      running_(false),
      port_(8888),
      protocol_type_(protocols::ProtocolType::TCP),
      last_result_json_("") {
}

VideoResultStorageServer::~VideoResultStorageServer() {
    stop();
}

bool VideoResultStorageServer::start(int port, protocols::ProtocolType protocol_type) {
    if (running_) {
        return true; // 已经在运行
    }

    // 设置服务端口和协议类型
    port_ = port;
    protocol_type_ = protocol_type;

    // 初始化存储目录
    if (!initialize_storage_directory()) {
        LOG_ERROR("Failed to initialize storage directory: " + storage_path_);
        return false;
    }

    // 创建新的结果文件
    if (!create_new_result_file()) {
        LOG_ERROR("Failed to create result file");
        return false;
    }

    // 初始化一个测试结果，以便客户端连接时有数据可用
    {
        std::lock_guard<std::mutex> lock(last_result_mutex_);
        Json::Value test_result;
        test_result["type"] = "test";
        test_result["frame_id"] = 0;
        test_result["task_type"] = "test";
        test_result["timestamp"] = static_cast<Json::Int64>(std::chrono::system_clock::now().time_since_epoch().count());
        test_result["message"] = "This is a test result. Real data will follow when available.";
        test_result["total_count"] = 0;
        test_result["detections"] = Json::Value(Json::arrayValue);

        // 使用StreamWriterBuilder直接写入字符串
        Json::StreamWriterBuilder writer;
        writer.settings_["indentation"] = "";
        std::ostringstream oss;
        std::unique_ptr<Json::StreamWriter> streamWriter(writer.newStreamWriter());
        streamWriter->write(test_result, &oss);
        last_result_json_ = oss.str();
    }

    // 创建协议实例
    protocol_ = protocols::create_protocol(protocol_type_);
    if (!protocol_) {
        LOG_ERROR("Failed to create protocol instance for type: " + std::to_string(static_cast<int>(protocol_type_)));
        return false;
    }

    // 设置新连接回调
    protocol_->set_new_connection_callback([this](void* client) {
        handle_new_connection(client);
    });

    // 启动协议服务
    if (!protocol_->start(port_)) {
        LOG_ERROR("Failed to start protocol service on port: " + std::to_string(port_));
        return false;
    }

    // 标记为运行状态
    running_ = true;

    // 启动存储线程
    storage_thread_ = std::thread(&VideoResultStorageServer::storage_thread_func, this);

    LOG_INFO("VideoResultStorageServer started with " + protocol_->get_protocol_name() +
             " protocol on port " + std::to_string(port_));
    return true;
}

void VideoResultStorageServer::stop() {
    if (!running_) {
        return; // 已经停止
    }

    // 标记为停止状态
    running_ = false;

    // 通知存储线程退出
    {
        std::lock_guard<std::mutex> lock(queue_mutex_);
        queue_cv_.notify_all();
    }

    // 停止协议服务
    if (protocol_) {
        protocol_->stop();
        protocol_.reset();
    }

    // 等待存储线程结束
    if (storage_thread_.joinable()) {
        storage_thread_.join();
    }

    // 关闭当前文件
    {
        std::lock_guard<std::mutex> lock(file_mutex_);
        if (current_file_.is_open()) {
            current_file_.close();
        }
    }

    LOG_INFO("VideoResultStorageServer stopped");
}

bool VideoResultStorageServer::add_result(const ai::FrameResult& result) {

    if (!running_) {
        return false;
    }

    // 添加结果到队列
    {
        std::lock_guard<std::mutex> lock(queue_mutex_);
        result_queue_.push_back(result);
    }

    // 通知存储线程有新结果
    queue_cv_.notify_one();

    // 如果是立即存储模式，直接刷新
    if (storage_mode_ == StorageMode::IMMEDIATE) {
        return flush();
    } else {
    }

    return true;
}

void VideoResultStorageServer::set_storage_path(const std::string& path) {
    if (running_) {
        return;
    }

    storage_path_ = path;
}

std::string VideoResultStorageServer::get_storage_path() const {
    return storage_path_;
}

void VideoResultStorageServer::set_storage_mode(StorageMode mode) {
    storage_mode_ = mode;
}

VideoResultStorageServer::StorageMode VideoResultStorageServer::get_storage_mode() const {
    return storage_mode_;
}

void VideoResultStorageServer::set_flush_interval(int interval_ms) {
    if (interval_ms > 0) {
        flush_interval_ms_ = interval_ms;
    }
}

int VideoResultStorageServer::get_flush_interval() const {
    return flush_interval_ms_;
}

std::string VideoResultStorageServer::get_current_file_path() const {
    return current_file_path_;
}

bool VideoResultStorageServer::is_running() const {
    return running_;
}

int VideoResultStorageServer::get_port() const {
    return port_;
}

int VideoResultStorageServer::get_client_count() const {
    return protocol_ ? protocol_->get_client_count() : 0;
}

bool VideoResultStorageServer::flush() {

    if (!running_) {
        return false;
    }

    std::vector<ai::FrameResult> results_to_process;

    // 获取所有待处理结果
    {
        std::lock_guard<std::mutex> lock(queue_mutex_);
        if (result_queue_.empty()) {
            return true; // 没有结果需要处理
        }

        results_to_process.assign(result_queue_.begin(), result_queue_.end());
        result_queue_.clear();
    }

    // 获取当前日期
    auto now = std::chrono::system_clock::now();
    auto now_time_t = std::chrono::system_clock::to_time_t(now);
    std::tm now_tm = *std::localtime(&now_time_t);

    std::ostringstream date_stream;
    date_stream << std::put_time(&now_tm, "%Y-%m-%d");
    std::string current_date = date_stream.str();

    // 如果日期变了，创建新文件
    {
        std::lock_guard<std::mutex> lock(file_mutex_);
        if (current_date != current_date_) {
            if (current_file_.is_open()) {
                current_file_.close();
            }
            if (!create_new_result_file()) {
                return false;
            }
        }
    }

    // 处理所有结果
    for (const auto& result : results_to_process) {
        // 将结果转换为JSON
        Json::Value json_result = result.to_json();

        // 配置JSON写入器 - 确保输出为单行，无缩进
        Json::StreamWriterBuilder writer;
        writer.settings_["indentation"] = "";     // 无缩进
        writer.settings_["commentStyle"] = "None"; // 不输出注释
        writer.settings_["enableYAMLCompatibility"] = false;
        writer.settings_["dropNullPlaceholders"] = false;
        writer.settings_["useSpecialFloats"] = false;

        // 将JSON转换为字符串
        std::ostringstream oss;
        std::unique_ptr<Json::StreamWriter> streamWriter(writer.newStreamWriter());
        streamWriter->write(json_result, &oss);
        std::string json_str = oss.str();

        // 写入文件
        {
            std::lock_guard<std::mutex> lock(file_mutex_);
            if (current_file_.is_open()) {
                current_file_ << json_str << std::endl;
                current_file_.flush();
            }
        }

        // 发送结果到协议服务
        send_result_to_protocol(json_str);
    }

    return true;
}

bool VideoResultStorageServer::initialize_storage_directory() {
    return ensure_directory_exists(storage_path_);
}

bool VideoResultStorageServer::create_new_result_file() {
    // 获取当前日期
    auto now = std::chrono::system_clock::now();
    auto now_time_t = std::chrono::system_clock::to_time_t(now);
    std::tm now_tm = *std::localtime(&now_time_t);

    std::ostringstream date_stream;
    date_stream << std::put_time(&now_tm, "%Y-%m-%d");
    current_date_ = date_stream.str();

    // 创建文件名
    std::ostringstream file_name_stream;
    file_name_stream << current_date_ << "_" << std::put_time(&now_tm, "%H-%M-%S") << ".txt";
    std::string file_name = file_name_stream.str();

    // 构建完整文件路径
    current_file_path_ = storage_path_ + "/" + file_name;

    // 打开文件
    if (current_file_.is_open()) {
        current_file_.close();
    }

    current_file_.open(current_file_path_, std::ios::out | std::ios::app);
    return current_file_.is_open();
}

bool VideoResultStorageServer::ensure_directory_exists(const std::string& dir_path) {
    try {
        if (!std::filesystem::exists(dir_path)) {
            return std::filesystem::create_directories(dir_path);
        }
        return true;
    } catch (const std::exception& e) {
        LOG_ERROR("Failed to create directory: " + std::string(e.what()));
        return false;
    }
}

void VideoResultStorageServer::storage_thread_func() {
    while (running_) {
        // 如果是定时存储模式，等待指定时间或有新结果
        if (storage_mode_ == StorageMode::TIMED) {
            std::unique_lock<std::mutex> lock(queue_mutex_);
            auto wait_result = queue_cv_.wait_for(lock,
                std::chrono::milliseconds(flush_interval_ms_),
                [this] { return !running_ || !result_queue_.empty(); });

            // 如果是因为超时而唤醒，且队列不为空，则刷新
            if (!wait_result && !result_queue_.empty()) {
                lock.unlock();
                flush();
            }
        } else {
            // 立即存储模式下，只在有新结果时被唤醒
            std::unique_lock<std::mutex> lock(queue_mutex_);
            queue_cv_.wait(lock, [this] { return !running_ || !result_queue_.empty(); });

            // 如果是因为有新结果而唤醒，则刷新
            if (!result_queue_.empty()) {
                lock.unlock();
                flush();
            }
        }

        // 如果服务已停止，退出循环
        if (!running_) {
            break;
        }
    }
}

void VideoResultStorageServer::handle_new_connection(void* client) {
    LOG_DEBUG("New client connected to " + get_protocol_name() + " protocol");

    // 发送最近的结果给新客户端
    if (!last_result_json_.empty()) {
        send_result_to_protocol(last_result_json_);
    }
}

bool VideoResultStorageServer::send_result_to_protocol(const std::string& json_str) {
    if (!protocol_ || !running_) {
        return false;
    }

    // 准备消息，确保以\n结尾
    std::string message_with_newline = json_str;
    if (message_with_newline.empty() || message_with_newline.back() != '\n') {
        message_with_newline += "\n";
    }

    // 保存最近的结果
    {
        std::lock_guard<std::mutex> lock(last_result_mutex_);
        last_result_json_ = message_with_newline;
    }

    // 发送结果到协议服务
    return protocol_->send_result(message_with_newline);
}

std::string VideoResultStorageServer::get_last_result() const {
    // 使用const_cast处理const方法中的互斥锁
    std::lock_guard<std::mutex> lock(*const_cast<std::mutex*>(&last_result_mutex_));
    return last_result_json_;
}

std::vector<std::string> VideoResultStorageServer::get_result_file_content(int max_results) const {
    std::vector<std::string> results;

    // 获取当前文件路径
    std::string file_path;
    {
        std::lock_guard<std::mutex> lock(*const_cast<std::mutex*>(&file_mutex_));
        file_path = current_file_path_;
    }

    // 检查文件是否存在
    if (file_path.empty()) {
        return results;
    }

    try {
        // 打开文件
        std::ifstream file(file_path, std::ios::binary | std::ios::ate); // 以二进制模式打开，定位到文件末尾
        if (!file.is_open()) {
            LOG_ERROR("Failed to open result file: " + file_path);
            return results;
        }

        const std::streampos fileSize = file.tellg();
        std::streampos pos = fileSize;
        char ch;
        int lineCount = 0;
        std::string buffer;

        // 从后向前逐字符读取
        while (pos > 0 && lineCount < max_results) {
            pos = pos - static_cast<std::streamoff>(1); 
            file.seekg(pos);
            file.get(ch);

            if (ch == '\n') {
                // 找到换行符，提取行内容
                if (!buffer.empty()) {
                    results.push_back(std::string(buffer.rbegin(), buffer.rend())); // 反转缓冲区内容
                    buffer.clear();
                    lineCount++;
                }
            } else {
                buffer.push_back(ch);
            }
        }

        // 反转结果顺序（因为是从后向前收集的）
        std::reverse(results.begin(), results.end());
        // 关闭文件
        file.close();


    } catch (const std::exception& e) {
        LOG_ERROR("Exception reading result file: " + std::string(e.what()));
    }

    return results;
}

// 添加协议类型和名称获取方法
protocols::ProtocolType VideoResultStorageServer::get_protocol_type() const {
    return protocol_type_;
}

std::string VideoResultStorageServer::get_protocol_name() const {
    return protocol_ ? protocol_->get_protocol_name() : "None";
}

// 添加Modbus寄存器映射方法
bool VideoResultStorageServer::set_modbus_register_map(const std::unordered_map<std::string, uint16_t>& register_map) {
    if (protocol_type_ != protocols::ProtocolType::MODBUS || !protocol_) {
        LOG_ERROR("Cannot set Modbus register map: protocol is not Modbus");
        return false;
    }

    auto modbus_protocol = std::dynamic_pointer_cast<protocols::ModbusResultProtocol>(protocol_);
    if (!modbus_protocol) {
        LOG_ERROR("Failed to cast protocol to ModbusResultProtocol");
        return false;
    }

    modbus_protocol->set_register_map(register_map);
    return true;
}

std::unordered_map<std::string, uint16_t> VideoResultStorageServer::get_modbus_register_map() const {
    if (protocol_type_ != protocols::ProtocolType::MODBUS || !protocol_) {
        return {};
    }

    auto modbus_protocol = std::dynamic_pointer_cast<protocols::ModbusResultProtocol>(protocol_);
    if (!modbus_protocol) {
        return {};
    }

    return modbus_protocol->get_register_map();
}

// 添加MQTT配置方法
bool VideoResultStorageServer::set_mqtt_server_address(const std::string& server_address) {
    if (protocol_type_ != protocols::ProtocolType::MQTT || !protocol_) {
        LOG_ERROR("Cannot set MQTT server address: protocol is not MQTT");
        return false;
    }

    auto mqtt_protocol = std::dynamic_pointer_cast<protocols::MqttResultProtocol>(protocol_);
    if (!mqtt_protocol) {
        LOG_ERROR("Failed to cast protocol to MqttResultProtocol");
        return false;
    }

    mqtt_protocol->set_server_address(server_address);
    return true;
}

bool VideoResultStorageServer::set_mqtt_topic(const std::string& topic) {
    if (protocol_type_ != protocols::ProtocolType::MQTT || !protocol_) {
        LOG_ERROR("Cannot set MQTT topic: protocol is not MQTT");
        return false;
    }

    auto mqtt_protocol = std::dynamic_pointer_cast<protocols::MqttResultProtocol>(protocol_);
    if (!mqtt_protocol) {
        LOG_ERROR("Failed to cast protocol to MqttResultProtocol");
        return false;
    }

    mqtt_protocol->set_topic(topic);
    return true;
}

bool VideoResultStorageServer::set_mqtt_client_id(const std::string& client_id) {
    if (protocol_type_ != protocols::ProtocolType::MQTT || !protocol_) {
        LOG_ERROR("Cannot set MQTT client ID: protocol is not MQTT");
        return false;
    }

    auto mqtt_protocol = std::dynamic_pointer_cast<protocols::MqttResultProtocol>(protocol_);
    if (!mqtt_protocol) {
        LOG_ERROR("Failed to cast protocol to MqttResultProtocol");
        return false;
    }

    mqtt_protocol->set_client_id(client_id);
    return true;
}

bool VideoResultStorageServer::set_mqtt_credentials(const std::string& username, const std::string& password) {
    if (protocol_type_ != protocols::ProtocolType::MQTT || !protocol_) {
        LOG_ERROR("Cannot set MQTT credentials: protocol is not MQTT");
        return false;
    }

    auto mqtt_protocol = std::dynamic_pointer_cast<protocols::MqttResultProtocol>(protocol_);
    if (!mqtt_protocol) {
        LOG_ERROR("Failed to cast protocol to MqttResultProtocol");
        return false;
    }

    mqtt_protocol->set_credentials(username, password);
    return true;
}

bool VideoResultStorageServer::set_mqtt_qos(int qos) {
    if (protocol_type_ != protocols::ProtocolType::MQTT || !protocol_) {
        LOG_ERROR("Cannot set MQTT QoS: protocol is not MQTT");
        return false;
    }

    auto mqtt_protocol = std::dynamic_pointer_cast<protocols::MqttResultProtocol>(protocol_);
    if (!mqtt_protocol) {
        LOG_ERROR("Failed to cast protocol to MqttResultProtocol");
        return false;
    }

    mqtt_protocol->set_qos(qos);
    return true;
}

std::string VideoResultStorageServer::get_mqtt_server_address() const {
    if (protocol_type_ != protocols::ProtocolType::MQTT || !protocol_) {
        return "";
    }

    auto mqtt_protocol = std::dynamic_pointer_cast<protocols::MqttResultProtocol>(protocol_);
    if (!mqtt_protocol) {
        return "";
    }

    return mqtt_protocol->get_server_address();
}

std::string VideoResultStorageServer::get_mqtt_topic() const {
    if (protocol_type_ != protocols::ProtocolType::MQTT || !protocol_) {
        return "";
    }

    auto mqtt_protocol = std::dynamic_pointer_cast<protocols::MqttResultProtocol>(protocol_);
    if (!mqtt_protocol) {
        return "";
    }

    return mqtt_protocol->get_topic();
}

std::string VideoResultStorageServer::get_mqtt_client_id() const {
    if (protocol_type_ != protocols::ProtocolType::MQTT || !protocol_) {
        return "";
    }

    auto mqtt_protocol = std::dynamic_pointer_cast<protocols::MqttResultProtocol>(protocol_);
    if (!mqtt_protocol) {
        return "";
    }

    return mqtt_protocol->get_client_id();
}

int VideoResultStorageServer::get_mqtt_qos() const {
    if (protocol_type_ != protocols::ProtocolType::MQTT || !protocol_) {
        return -1;
    }

    auto mqtt_protocol = std::dynamic_pointer_cast<protocols::MqttResultProtocol>(protocol_);
    if (!mqtt_protocol) {
        return -1;
    }

    return mqtt_protocol->get_qos();
}

} // namespace core

