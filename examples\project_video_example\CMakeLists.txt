cmake_minimum_required(VERSION 3.19)
project(ProjectVideoExample)

# 添加可执行文件
add_executable(project_video_example project_video_example.cpp)

# 包含头文件目录
target_include_directories(project_video_example PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}/../../include
)

# 链接库
target_link_libraries(project_video_example PRIVATE
    AiVideoCore
)
# 添加链接选项，使用 --wrap=pthread_yield
target_link_options(project_video_example PRIVATE -Wl,--wrap=pthread_yield)

# 链接 Boost 库
find_package(boost_filesystem CONFIG REQUIRED)
target_link_libraries(project_video_example PUBLIC Boost::filesystem)
find_package(boost_log CONFIG REQUIRED)
target_link_libraries(project_video_example PUBLIC Boost::log)

# 安装目标
install(TARGETS project_video_example DESTINATION release)

# 确保插件目录存在
install(CODE "file(MAKE_DIRECTORY \${CMAKE_INSTALL_PREFIX}/release/plugins/task)")

# 复制依赖的DLL文件到输出目录
if(WIN32)
    add_custom_command(TARGET project_video_example POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
            $<TARGET_FILE:AiVideoCore>
            $<TARGET_FILE_DIR:project_video_example>
    )
endif()
