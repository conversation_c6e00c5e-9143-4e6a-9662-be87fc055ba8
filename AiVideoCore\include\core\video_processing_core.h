#pragma once

#include <functional>
#include <map>
#include <memory>
#include <string>
#include <vector>

#include "../aivideocore_export.h"
#include "../ai/ai_model_manager.h"
#include "../ai/ai_processor.h"
#include "../ai/frame_result.h"
#include "../ai/plugins/task_plugin.h"
#include "../ai/plugins/python_script_manager.h"
#include "project.h"
#include "video_result_storage_server.h"
#include "../utils/video_frame_provider.h"

// Forward declarations
namespace cv {
    class Mat;
}

namespace core {

/**
 * @brief 视频处理核心类，封装所有视频处理逻辑，与GUI无关
 */
class AIVIDEOCORE_API VideoProcessingCore {
public:
    /**
     * @brief 构造函数
     */
    VideoProcessingCore();

    /**
     * @brief 析构函数
     */
    ~VideoProcessingCore();

    /**
     * @brief 初始化VisionFlow环境
     * @param licenseId 许可证ID
     * @param serverAddr 服务器地址
     */
    static void initialize_visionflow(const std::string& licenseId, const std::string& serverAddr);

    /**
     * @brief 加载AI模型
     * @param modelPath 模型文件路径
     * @param promptValue 提示词
     * @param scoreValue 置信度阈值
     * @param iouValue IOU阈值
     * @return 是否加载成功
     */
    bool load_model(const std::string& modelPath, const std::string& promptValue = "",
                  double scoreValue = 0.5, int iouValue = 70);

    /**
     * @brief 获取当前模型路径
     * @return 模型路径
     */
    std::string get_model_path() const;

    /**
     * @brief 获取输入节点ID
     * @return 输入节点ID
     */
    std::string get_input_node_id() const;

    /**
     * @brief 获取输出节点ID
     * @return 输出节点ID
     */
    std::string get_output_node_id() const;

    /**
     * @brief 初始化模型运行时
     * @param inputNodeId 输入节点ID
     * @param outputNodeId 输出节点ID
     * @return 是否初始化成功
     */
    bool initialize_runtime(const std::string& inputNodeId, const std::string& outputNodeId);

    /**
     * @brief 打开视频文件
     * @param filePath 视频文件路径
     * @return 是否成功打开
     */
    bool open_video_file(const std::string& filePath);

    /**
     * @brief 打开摄像头
     * @param cameraId 摄像头ID
     * @return 是否成功打开
     */
    bool open_camera(int cameraId);

    /**
     * @brief 打开RTSP视频流
     * @param url RTSP URL
     * @return 是否成功打开
     */
    bool open_rtsp_stream(const std::string& url);

    /**
     * @brief 关闭视频源
     */
    void close_video();

    /**
     * @brief 处理下一帧
     * @param enableAI 是否启用AI处理
     * @return 处理结果
     */
    ai::FrameResult process_next_frame(bool enableAI = true);

    /**
     * @brief 处理指定帧
     * @param frameIndex 帧索引
     * @param enableAI 是否启用AI处理
     * @return 处理结果
     */
    ai::FrameResult process_frame(int frameIndex, bool enableAI = true);

    /**
     * @brief 处理当前帧
     * @param frame 输入帧
     * @param enableAI 是否启用AI处理
     * @return 处理结果
     */
    ai::FrameResult process_frame(const cv::Mat& frame, bool enableAI = true);

    /**
     * @brief 注册任务处理插件
     * @param plugin 插件指针
     * @return 是否注册成功
     */
    bool register_plugin(std::shared_ptr<ai::plugins::TaskPlugin> plugin);

    /**
     * @brief 注销任务处理插件
     * @param name 插件名称
     * @return 是否注销成功
     */
    bool unregister_plugin(const std::string& name);

    /**
     * @brief 启用插件（并保存到全局设置）
     * @param name 插件名称
     * @return 是否成功启用
     */
    bool enable_plugin(const std::string& name);

    /**
     * @brief 禁用插件（并保存到全局设置）
     * @param name 插件名称
     * @return 是否成功禁用
     */
    bool disable_plugin(const std::string& name);

    /**
     * @brief 启用插件（不保存到全局设置）
     * @param name 插件名称
     * @param enabled 是否启用
     * @return 是否成功设置
     */
    bool set_plugin_enabled(const std::string& name, bool enabled);

    /**
     * @brief 禁用所有插件（不保存到全局设置）
     */
    void disable_all_plugins();

    /**
     * @brief 从指定目录加载插件
     * @param directory 插件目录路径
     * @return 成功加载的插件数量
     */
    int load_plugins_from_directory(const std::string& directory);

    /**
     * @brief 获取所有插件名称
     * @return 插件名称列表
     */
    std::vector<std::string> get_plugin_names() const;

    /**
     * @brief 检查插件是否已启用
     * @param name 插件名称
     * @return 是否已启用
     */
    bool is_plugin_enabled(const std::string& name) const;

    /**
     * @brief 获取插件参数
     * @param name 插件名称
     * @return 参数映射
     */
    std::map<std::string, std::string> get_plugin_params(const std::string& name) const;

    /**
     * @brief 设置插件参数
     * @param name 插件名称
     * @param params 参数映射
     * @return 是否设置成功
     */
    bool set_plugin_params(const std::string& name, const std::map<std::string, std::string>& params);

    /**
     * @brief 初始化Python帧处理器
     */
    void initialize_python_frame_processor();

    /**
     * @brief 初始化C++帧处理器
     */
    void initialize_cpp_frame_processor();

    /**
     * @brief 设置Python帧处理器脚本
     * @param scriptPath 脚本路径
     * @param params 脚本参数
     * @return 是否设置成功
     */
    bool set_python_frame_processor_script(const std::string& scriptPath,
                                      const std::map<std::string, std::string>& params);

    /**
     * @brief 启用Python帧处理
     * @param enable 是否启用
     */
    void enable_python_frame_processor(bool enable);

    /**
     * @brief 设置抽帧间隔，即每隔多少帧进行一次AI检测
     * @param interval 抽帧间隔，默认为1（每帧都检测）
     */
    void set_frame_skip_interval(int interval);

    /**
     * @brief 获取当前抽帧间隔
     * @return 抽帧间隔
     */
    int get_frame_skip_interval() const;

    /**
     * @brief 获取视频总帧数
     * @return 总帧数
     */
    int get_total_frames() const;

    /**
     * @brief 获取当前帧索引
     * @return 当前帧索引
     */
    int get_current_frame() const;

    /**
     * @brief 获取视频帧率
     * @return 帧率
     */
    double get_fps() const;

    /**
     * @brief 检查视频是否已打开
     * @return 是否已打开
     */
    bool is_video_opened() const;

    /**
     * @brief 检查模型是否已加载
     * @return 是否已加载
     */
    bool is_model_loaded() const;

    /**
     * @brief 获取AI处理器
     * @return AI处理器指针
     */
    std::shared_ptr<ai::AiProcessor> get_ai_processor() const;

    /**
     * @brief 获取模型管理器
     * @return 模型管理器指针
     */
    std::shared_ptr<ai::AiModelManager> get_model_manager() const;

    /**
     * @brief 获取视频帧提供者
     * @return 视频帧提供者指针
     */
    std::shared_ptr<utils::VideoFrameProvider> get_video_provider() const;

    /**
     * @brief 启动结果存储服务（支持多种协议）
     * @param storage_path 存储路径
     * @param mode 存储模式
     * @param port 服务端口
     * @param flush_interval_ms 刷新间隔（毫秒）
     * @param protocol_type 协议类型
     * @return 是否成功启动
     */
    bool start_result_storage_server(const std::string& storage_path = "results",
                                   VideoResultStorageServer::StorageMode mode = VideoResultStorageServer::StorageMode::IMMEDIATE,
                                   int port = 8888,
                                   int flush_interval_ms = 5000,
                                   protocols::ProtocolType protocol_type = protocols::ProtocolType::TCP);

    /**
     * @brief 获取协议类型名称
     * @param type 协议类型
     * @return 协议类型名称
     */
    std::string get_protocol_type_name(protocols::ProtocolType type) const;

    /**
     * @brief 停止结果存储服务
     */
    void stop_result_storage_server();

    /**
     * @brief 获取结果存储服务
     * @return 结果存储服务指针
     */
    std::shared_ptr<VideoResultStorageServer> get_result_storage_server() const;

    /**
     * @brief 添加结果到存储服务
     * @param result 帧处理结果
     * @return 是否成功添加
     */
    bool add_result_to_storage(const ai::FrameResult& result);

    /**
     * @brief 设置Modbus寄存器映射（仅在使用Modbus协议时有效）
     * @param register_map 寄存器映射表
     * @return 是否成功设置
     */
    bool set_modbus_register_map(const std::unordered_map<std::string, uint16_t>& register_map);

    /**
     * @brief 获取Modbus寄存器映射（仅在使用Modbus协议时有效）
     * @return 寄存器映射表
     */
    std::unordered_map<std::string, uint16_t> get_modbus_register_map() const;

    /**
     * @brief 根据检测结果提取帧，存储包含或不包含特定目标的原始帧
     * @param outputPath 输出路径
     * @param targetClass 目标类别，空字符串表示任意类别
     * @param includeTarget true表示保存包含目标的帧，false表示保存不包含目标的帧
     * @param progressCallback 进度回调函数，参数为当前进度百分比(0-100)和当前已提取帧数
     * @return 提取的帧数
     */
    int extract_frames_by_detection(const std::string& outputPath,
                                 const std::string& targetClass,
                                 bool includeTarget,
                                 std::function<bool(int, int)> progressCallback = nullptr);

    /**
     * @brief 从项目加载插件参数
     * @param project 项目指针
     * @return 是否加载成功
     */
    bool load_params_from_project(const std::shared_ptr<Project>& project);

    /**
     * @brief 根据项目配置设置插件参数和启用状态
     * @param project 项目指针，用于获取插件参数和启用状态
     * @return 成功设置参数的插件数量
     */
    int set_plugin_params_from_project(const std::shared_ptr<Project>& project);

    /**
     * @brief 从指定目录加载插件
     * @param plugin_directory 插件目录路径
     * @return 成功加载的插件数量
     */
    int load_plugins_from_project_directory(const std::string& plugin_directory);

    /**
     * @brief 从指定目录加载插件并设置参数（兼容旧接口）
     * @param plugin_directory 插件目录路径
     * @param project 项目指针，用于获取插件参数
     * @return 成功加载的插件数量
     */
    int load_plugins_and_set_params(const std::string& plugin_directory, const std::shared_ptr<Project>& project);

    /**
     * @brief 获取当前正在处理的原始图像
     * @return 当前原始帧（cv::Mat）
     */
    cv::Mat get_current_raw_frame() const;

private:
    /**
     * @brief 初始化默认插件
     */
    void initialize_default_plugins();

private:
    std::shared_ptr<utils::VideoFrameProvider> video_provider_;  ///< 视频帧提供者
    std::shared_ptr<ai::AiModelManager> model_manager_;          ///< AI模型管理器
    std::shared_ptr<ai::AiProcessor> ai_processor_;              ///< AI处理器
    std::shared_ptr<VideoResultStorageServer> result_storage_server_; ///< 结果存储服务
    std::shared_ptr<ai::plugins::PythonScriptManager> python_script_manager_; ///< Python脚本管理器

    std::string input_node_id_;                                   ///< 输入节点ID
    std::string output_node_id_;                                  ///< 输出节点ID
    std::string model_path_;                                      ///< 模型文件路径
};

} // namespace core
