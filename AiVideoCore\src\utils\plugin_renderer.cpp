#include "utils/plugin_renderer.h"
#include "utils/cv_utils.h"
#include <opencv2/opencv.hpp>
#include <json/json.h>
#include "ai/frame_result.h"

namespace utils {

// 实现 render_frame_result_json_value 函数
bool PluginRenderer::render_frame_result_json_value(cv::Mat& frame, const Json::Value& json_value) {
    // 直接调用现有的 render 函数处理 JSON 对象
    return render(frame, json_value);
}

// 实现 render_frame_result 函数
bool PluginRenderer::render_frame_result(cv::Mat& frame, const ai::FrameResult& result) {
    // 如果 ext_info 为空，则无需渲染
    if (result.ext_info.empty()) {
        return false;
    }

    try {
        // 解析 ext_info 字符串为 JSON
        Json::Value ext_info_json;
        Json::Reader reader;

        if (!reader.parse(result.ext_info, ext_info_json)) {
            return false;
        }

        // 检查是否包含 render_info
        if (!ext_info_json.isMember("render_info") || !ext_info_json["render_info"].isObject()) {
            return false;
        }

        // 获取 render_info 并渲染
        const Json::Value& render_info = ext_info_json["render_info"];
        return render(frame, render_info);
    } catch (const std::exception& e) {
        // 解析或渲染过程中出现异常
        return false;
    }
}

// 实现 render_frame_result_json 函数
bool PluginRenderer::render_frame_result_json(cv::Mat& frame, const std::string& json_str) {
    // 解析 JSON 字符串
    Json::Value root;
    Json::Reader reader;

    if (!reader.parse(json_str, root)) {
        return false;
    }

    // 调用 render_frame_result_json_value 函数处理 JSON 对象
    return render_frame_result_json_value(frame, root);
}

PluginRenderer::PluginRenderer() {
}

PluginRenderer::~PluginRenderer() {
}

bool PluginRenderer::render(cv::Mat& frame, const Json::Value& render_info) {
    if (render_info.isNull() || !render_info.isObject()) {
        return false;
    }

    // 检查是否需要渲染
    if (render_info.isMember("should_render") && !render_info["should_render"].asBool()) {
        return false;
    }

    // 渲染自定义元素（如果有）- 先渲染这些元素，以便其他元素可以覆盖它们
    if (render_info.isMember("custom_elements") && render_info["custom_elements"].isArray()) {
        render_custom_elements(frame, render_info["custom_elements"]);
    }

    // 渲染帧信息
    if (render_info.isMember("frame_info") && render_info["frame_info"].isObject()) {
        render_frame_info(frame, render_info["frame_info"]);
    }

    // 渲染状态信息
    if (render_info.isMember("status") && render_info["status"].isObject()) {
        render_status(frame, render_info["status"]);
    }

    // 渲染已完成步骤
    if (render_info.isMember("completed_steps") && render_info["completed_steps"].isArray()) {
        render_completed_steps(frame, render_info["completed_steps"]);
    }

    // 渲染跟踪目标
    if (render_info.isMember("tracks") && render_info["tracks"].isArray()) {
        render_tracks(frame, render_info["tracks"]);
    }

    return true;
}

void PluginRenderer::render_frame_info(cv::Mat& frame, const Json::Value& frame_info) {
    // 渲染帧计数
    if (frame_info.isMember("frame_count")) {
        std::string frame_text = "帧 " + frame_info["frame_count"].asString();
        utils::putTextZH(frame, frame_text, cv::Point(10, 30), 0.7, cv::Scalar(0, 255, 0), 2);
    }

    // 渲染类别文本
    if (frame_info.isMember("category_text")) {
        std::string category_text = "类别: " + frame_info["category_text"].asString();
        utils::putTextZH(frame, category_text, cv::Point(10, 60), 0.7, cv::Scalar(0, 255, 0), 2);
    }
}

void PluginRenderer::render_status(cv::Mat& frame, const Json::Value& status) {
    // 渲染当前步骤和进度
    if (status.isMember("current_step") && !status["current_step"].asString().empty()) {
        std::string progress = "";
        if (status.isMember("step_progress")) {
            progress = "(" + status["step_progress"].asString() + ")";
        }

        std::string step_text = "当前步骤" + progress + ": " + status["current_step"].asString();
        utils::putTextZH(frame, step_text, cv::Point(10, 90), 0.7, cv::Scalar(0, 255, 0), 2);
    }

    // 渲染状态文本
    if (status.isMember("status_text") && !status["status_text"].asString().empty()) {
        // 如果是错误状态，使用红色
        cv::Scalar color = (status.isMember("is_error") && status["is_error"].asBool())
            ? cv::Scalar(0, 0, 255) : cv::Scalar(0, 255, 0);

        std::string status_text = "状态: " + status["status_text"].asString();
        utils::putTextZH(frame, status_text, cv::Point(10, 120), 0.7, color, 2);
    }
}

void PluginRenderer::render_completed_steps(cv::Mat& frame, const Json::Value& completed_steps) {
    if (completed_steps.size() > 0) {
        // 绘制标题
        std::string completed_text = "已完成的合规步骤:";
        utils::putTextZH(frame, completed_text, cv::Point(10, 150), 0.7, cv::Scalar(0, 255, 255), 2);

        // 逐个显示已完成的步骤
        for (int i = 0; i < completed_steps.size(); ++i) {
            int y_pos = 180 + i * 30;

            if (completed_steps[i].isMember("step_name")) {
                std::string step_text = std::to_string(i+1) + ". " + completed_steps[i]["step_name"].asString();
                utils::putTextZH(frame, step_text, cv::Point(30, y_pos), 0.7, cv::Scalar(0, 255, 255), 2);
            }
        }
    }
}

void PluginRenderer::render_tracks(cv::Mat& frame, const Json::Value& tracks) {
    for (int i = 0; i < tracks.size(); ++i) {
        const Json::Value& track = tracks[i];

        if (track.isMember("track_id") && track.isMember("bbox") && track.isMember("class")) {
            int track_id = track["track_id"].asInt();
            std::string cls = track["class"].asString();

            // 获取边界框
            const Json::Value& bbox = track["bbox"];
            if (bbox.isArray() && bbox.size() == 4) {
                int x = bbox[0].asInt();
                int y = bbox[1].asInt();
                int w = bbox[2].asInt();
                int h = bbox[3].asInt();

                // 绘制边界框
                cv::rectangle(frame, cv::Rect(x, y, w, h), cv::Scalar(0, 255, 0), 2);

                // 绘制ID和类别
                std::string label = "ID:" + std::to_string(track_id) + " " + cls;
                utils::putTextZH(frame, label, cv::Point(x, y - 10), 0.5, cv::Scalar(0, 255, 0), 2);
            }
        }
    }
}

void PluginRenderer::render_custom_elements(cv::Mat& frame, const Json::Value& elements) {
    for (int i = 0; i < elements.size(); ++i) {
        const Json::Value& element = elements[i];

        if (element.isMember("type")) {
            std::string type = element["type"].asString();

            if (type == "line" && element.isMember("points")) {
                // 绘制直线
                const Json::Value& points = element["points"];
                if (points.isArray() && points.size() >= 2) {
                    const Json::Value& p1 = points[0];
                    const Json::Value& p2 = points[1];

                    if (p1.isArray() && p1.size() >= 2 && p2.isArray() && p2.size() >= 2) {
                        cv::Point pt1(p1[0].asInt(), p1[1].asInt());
                        cv::Point pt2(p2[0].asInt(), p2[1].asInt());

                        // 获取颜色和线宽
                        cv::Scalar color(0, 255, 0);  // 默认绿色
                        int thickness = 2;  // 默认线宽

                        if (element.isMember("color") && element["color"].isArray() && element["color"].size() >= 3) {
                            color = cv::Scalar(
                                element["color"][0].asInt(),
                                element["color"][1].asInt(),
                                element["color"][2].asInt()
                            );
                        }

                        if (element.isMember("thickness")) {
                            thickness = element["thickness"].asInt();
                        }

                        // 绘制直线
                        cv::line(frame, pt1, pt2, color, thickness);
                    }
                }
            }
            else if (type == "polygon" && element.isMember("points")) {
                // 绘制多边形
                const Json::Value& points = element["points"];
                if (points.isArray() && points.size() >= 3) {
                    std::vector<cv::Point> contour;
                    for (int j = 0; j < points.size(); ++j) {
                        const Json::Value& p = points[j];
                        if (p.isArray() && p.size() >= 2) {
                            contour.push_back(cv::Point(p[0].asInt(), p[1].asInt()));
                        }
                    }

                    if (contour.size() >= 3) {
                        // 获取颜色和线宽
                        cv::Scalar color(0, 255, 0);  // 默认绿色
                        int thickness = 2;  // 默认线宽
                        bool fill = false;  // 默认不填充
                        double fill_alpha = 0.3;  // 默认填充透明度

                        if (element.isMember("color") && element["color"].isArray() && element["color"].size() >= 3) {
                            color = cv::Scalar(
                                element["color"][0].asInt(),
                                element["color"][1].asInt(),
                                element["color"][2].asInt()
                            );
                        }

                        if (element.isMember("thickness")) {
                            thickness = element["thickness"].asInt();
                        }

                        if (element.isMember("fill")) {
                            fill = element["fill"].asBool();
                        }

                        if (element.isMember("fill_alpha")) {
                            fill_alpha = element["fill_alpha"].asDouble();
                        }

                        // 绘制多边形
                        std::vector<std::vector<cv::Point>> contours;
                        contours.push_back(contour);

                        if (fill) {
                            // 创建一个透明的蒙版
                            cv::Mat mask = cv::Mat::zeros(frame.size(), CV_8UC3);
                            cv::fillPoly(mask, contours, color);
                            cv::addWeighted(mask, fill_alpha, frame, 1.0, 0, frame);
                        }

                        // 绘制多边形边缘
                        cv::polylines(frame, contours, true, color, thickness);
                    }
                }
            }
            else if (type == "rectangle" && element.isMember("bbox")) {
                // 绘制矩形
                const Json::Value& bbox = element["bbox"];
                if (bbox.isArray() && bbox.size() >= 4) {
                    int x = bbox[0].asInt();
                    int y = bbox[1].asInt();
                    int w = bbox[2].asInt();
                    int h = bbox[3].asInt();

                    // 获取颜色和线宽
                    cv::Scalar color(0, 255, 0);  // 默认绿色
                    int thickness = 2;  // 默认线宽

                    if (element.isMember("color") && element["color"].isArray() && element["color"].size() >= 3) {
                        color = cv::Scalar(
                            element["color"][0].asInt(),
                            element["color"][1].asInt(),
                            element["color"][2].asInt()
                        );
                    }

                    if (element.isMember("thickness")) {
                        thickness = element["thickness"].asInt();
                    }

                    // 绘制矩形
                    cv::rectangle(frame, cv::Rect(x, y, w, h), color, thickness);
                }
            }
            else if (type == "text" && element.isMember("text") && element.isMember("position")) {
                // 绘制文本
                std::string text = element["text"].asString();
                const Json::Value& position = element["position"];

                if (position.isArray() && position.size() >= 2) {
                    cv::Point pos(position[0].asInt(), position[1].asInt());

                    // 获取颜色、字体大小和线宽
                    cv::Scalar color(0, 255, 0);  // 默认绿色
                    double fontSize = 0.7;  // 默认字体大小
                    int thickness = 2;  // 默认线宽

                    if (element.isMember("color") && element["color"].isArray() && element["color"].size() >= 3) {
                        color = cv::Scalar(
                            element["color"][0].asInt(),
                            element["color"][1].asInt(),
                            element["color"][2].asInt()
                        );
                    }

                    if (element.isMember("font_size")) {
                        fontSize = element["font_size"].asDouble();
                    }

                    if (element.isMember("thickness")) {
                        thickness = element["thickness"].asInt();
                    }

                    // 绘制文本
                    utils::putTextZH(frame, text, pos, fontSize, color, thickness);
                }
            }
            else if (type == "circle" && element.isMember("center") && element.isMember("radius")) {
                // 绘制圆
                const Json::Value& center = element["center"];
                int radius = element["radius"].asInt();

                if (center.isArray() && center.size() >= 2) {
                    cv::Point center_pt(center[0].asInt(), center[1].asInt());

                    // 获取颜色和线宽
                    cv::Scalar color(0, 255, 0);  // 默认绿色
                    int thickness = 2;  // 默认线宽

                    if (element.isMember("color") && element["color"].isArray() && element["color"].size() >= 3) {
                        color = cv::Scalar(
                            element["color"][0].asInt(),
                            element["color"][1].asInt(),
                            element["color"][2].asInt()
                        );
                    }

                    if (element.isMember("thickness")) {
                        thickness = element["thickness"].asInt();
                    }

                    // 绘制圆
                    cv::circle(frame, center_pt, radius, color, thickness);
                }
            }
        }
    }
}

} // namespace ui
