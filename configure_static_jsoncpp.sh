#!/bin/bash
# 配置使用静态链接 jsoncpp 的构建脚本

echo "正在配置项目使用静态链接的 jsoncpp..."

# 清理之前的构建
if [ -d "build" ]; then
    echo "清理之前的构建目录..."
    rm -rf build
fi

# 创建构建目录
mkdir -p build
cd build

echo "运行 CMake 配置..."
# 使用 vcpkg 的静态链接模式
cmake .. \
    -DCMAKE_BUILD_TYPE=Release \
    -DVCPKG_TARGET_TRIPLET=x64-linux-static \
    -DCMAKE_TOOLCHAIN_FILE=/home/<USER>/vcpkg/scripts/buildsystems/vcpkg.cmake

if [ $? -ne 0 ]; then
    echo "CMake 配置失败"
    exit 1
fi

echo ""
echo "配置完成！现在构建项目:"
echo "  make -j\$(nproc)"
echo ""
echo "注意: 使用静态链接后，生成的可执行文件会更大，但不需要额外的共享库文件。"
