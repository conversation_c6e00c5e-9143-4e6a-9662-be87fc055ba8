@echo off
REM 恢复原始 vcpkg jsoncpp.dll 的脚本

echo 正在恢复原始的 jsoncpp.dll...

REM 定义备份文件位置
set VCPKG_JSONCPP_RELEASE=build\vcpkg_installed\x64-windows\bin\jsoncpp.dll
set VCPKG_JSONCPP_DEBUG=build\vcpkg_installed\x64-windows\debug\bin\jsoncpp.dll

REM 恢复 Release 版本
if exist "%VCPKG_JSONCPP_RELEASE%.backup" (
    echo 恢复 Release 版本...
    copy "%VCPKG_JSONCPP_RELEASE%.backup" "%VCPKG_JSONCPP_RELEASE%"
    if %ERRORLEVEL% neq 0 (
        echo 恢复 Release 版本失败
    ) else (
        echo Release 版本恢复成功
        del "%VCPKG_JSONCPP_RELEASE%.backup"
    )
) else (
    echo 未找到 Release 版本的备份文件
)

REM 恢复 Debug 版本
if exist "%VCPKG_JSONCPP_DEBUG%.backup" (
    echo 恢复 Debug 版本...
    copy "%VCPKG_JSONCPP_DEBUG%.backup" "%VCPKG_JSONCPP_DEBUG%"
    if %ERRORLEVEL% neq 0 (
        echo 恢复 Debug 版本失败
    ) else (
        echo Debug 版本恢复成功
        del "%VCPKG_JSONCPP_DEBUG%.backup"
    )
) else (
    echo 未找到 Debug 版本的备份文件
)

echo.
echo 恢复完成！现在重新构建项目以使用原始的 vcpkg jsoncpp:
echo   cd build
echo   cmake --build . --config Release

pause
