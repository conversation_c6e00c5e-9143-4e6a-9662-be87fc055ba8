#pragma once

#include <string>
#include <vector>
#include <map>
#include "aivideocore_export.h"

// 前向声明
namespace cv {
    class Mat;
}

namespace Json {
    class Value;
}

namespace ai {
    struct FrameResult;
}

namespace utils {

/**
 * @brief 插件渲染器类，用于根据插件返回的渲染信息渲染图像
 */
class AIVIDEOCORE_API PluginRenderer {
public:
    /**
     * @brief 构造函数
     */
    PluginRenderer();

    /**
     * @brief 析构函数
     */
    ~PluginRenderer();

    /**
     * @brief 根据渲染信息渲染图像
     * @param frame 输入/输出图像
     * @param render_info 渲染信息
     * @return 是否渲染成功
     */
    bool render(cv::Mat& frame, const Json::Value& render_info);

    /**
     * @brief 根据 FrameResult.to_json() 的 JSON 字符串渲染图像
     * @param frame 输入/输出图像
     * @param json_str FrameResult.to_json() 的 JSON 字符串
     * @return 是否渲染成功
     */
    bool render_frame_result_json(cv::Mat& frame, const std::string& json_str);

    /**
     * @brief 根据 FrameResult.to_json() 的 JSON 值渲染图像
     * @param frame 输入/输出图像
     * @param json_value FrameResult.to_json() 的 JSON 值
     * @return 是否渲染成功
     */
    bool render_frame_result_json_value(cv::Mat& frame, const Json::Value& json_value);

    /**
     * @brief 根据 FrameResult 对象渲染图像，只渲染 ext_info 中的 render_info 部分
     * @param frame 输入/输出图像
     * @param result FrameResult 对象
     * @return 是否渲染成功
     */
    bool render_frame_result(cv::Mat& frame, const ai::FrameResult& result);

private:
    /**
     * @brief 渲染帧信息
     * @param frame 输入/输出图像
     * @param frame_info 帧信息
     */
    void render_frame_info(cv::Mat& frame, const Json::Value& frame_info);

    /**
     * @brief 渲染状态信息
     * @param frame 输入/输出图像
     * @param status 状态信息
     */
    void render_status(cv::Mat& frame, const Json::Value& status);

    /**
     * @brief 渲染已完成步骤
     * @param frame 输入/输出图像
     * @param completed_steps 已完成步骤列表
     */
    void render_completed_steps(cv::Mat& frame, const Json::Value& completed_steps);

    /**
     * @brief 渲染跟踪目标
     * @param frame 输入/输出图像
     * @param tracks 跟踪目标列表
     */
    void render_tracks(cv::Mat& frame, const Json::Value& tracks);

    /**
     * @brief 渲染自定义元素
     * @param frame 输入/输出图像
     * @param elements 自定义元素列表
     */
    void render_custom_elements(cv::Mat& frame, const Json::Value& elements);
};

} // namespace ui
