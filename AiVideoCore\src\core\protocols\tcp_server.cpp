#include "core/protocols/tcp_server.h"
#include "utils/log_manager.h"
#include <cstring>
#include <iostream>

namespace core {

TcpConnection::TcpConnection(int socket)
    : socket_(socket), is_connected_(true) {
#ifdef _WIN32
    u_long mode = 1; // Non-blocking mode
    if (ioctlsocket(socket_, FIONBIO, &mode) != 0) {
        LOG_ERROR("Failed to set socket to non-blocking mode: " + std::to_string(WSAGetLastError()));
    }
#else
    int flags = fcntl(socket_, F_GETFL, 0);
    if (flags == -1 || fcntl(socket_, F_SETFL, flags | O_NONBLOCK) == -1) {
        LOG_ERROR("Failed to set socket to non-blocking mode");
    }
#endif
}

TcpConnection::~TcpConnection() {
    if (socket_ >= 0) {
#ifdef _WIN32
        closesocket(socket_);
#else
        close(socket_);
#endif
    }
}

bool TcpConnection::send(const std::string& message) {
    std::lock_guard<std::mutex> lock(send_mutex_);
    if (!is_connected_) {
        return false;
    }

    int result = ::send(socket_, message.c_str(), message.size(), 0);
    if (result < 0) {
#ifdef _WIN32
        int error = WSAGetLastError();
        if (error != WSAEWOULDBLOCK) {
#else
        if (errno != EWOULDBLOCK) {
#endif
            is_connected_ = false;
        }
        return false;
    }
    return true;
}

TcpServer::TcpServer(int port, std::function<void(std::shared_ptr<TcpConnection>)> on_new_connection)
    : listen_socket_(-1), running_(true), on_new_connection_(on_new_connection) {
    listen_socket_ = socket(AF_INET, SOCK_STREAM, 0);
    if (listen_socket_ < 0) {
        throw std::runtime_error("Failed to create socket");
    }

    int opt = 1;
#ifdef _WIN32
    setsockopt(listen_socket_, SOL_SOCKET, SO_REUSEADDR, (const char*)&opt, sizeof(opt));
#else
    setsockopt(listen_socket_, SOL_SOCKET, SO_REUSEADDR, &opt, sizeof(opt));
#endif

    sockaddr_in server_addr;
    std::memset(&server_addr, 0, sizeof(server_addr));
    server_addr.sin_family = AF_INET;
    server_addr.sin_addr.s_addr = INADDR_ANY;
    server_addr.sin_port = htons(port);

    if (bind(listen_socket_, (struct sockaddr*)&server_addr, sizeof(server_addr)) < 0) {
        throw std::runtime_error("Failed to bind socket");
    }

    if (listen(listen_socket_, SOMAXCONN) < 0) {
        throw std::runtime_error("Failed to listen on socket");
    }

    accept_thread_ = std::thread(&TcpServer::accept_thread_func, this);
}

TcpServer::~TcpServer() {
    stop();
}

void TcpServer::stop() {
    running_ = false;

    // Close the listening socket to interrupt accept()
    if (listen_socket_ >= 0) {
#ifdef _WIN32
        closesocket(listen_socket_);
#else
        close(listen_socket_);
#endif
        listen_socket_ = -1;
    }

    // Join the accept thread
    if (accept_thread_.joinable()) {
        accept_thread_.join();
    }
}

void TcpServer::accept_thread_func() {
    while (running_) {
        sockaddr_in client_addr;
        socklen_t client_len = sizeof(client_addr);
        int client_socket = accept(listen_socket_, (struct sockaddr*)&client_addr, &client_len);
        if (client_socket < 0) {
#ifdef _WIN32
            if (WSAGetLastError() != WSAEWOULDBLOCK) {
#else
            if (errno != EWOULDBLOCK) {
#endif
                LOG_ERROR("Failed to accept connection");
            }
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
            continue;
        }

        auto connection = std::make_shared<TcpConnection>(client_socket);
        on_new_connection_(connection);
    }
}

} // namespace core
