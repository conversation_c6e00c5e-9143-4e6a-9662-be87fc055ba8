#include "core/protocols/tcp_result_protocol.h"
#include "core/protocols/tcp_server.h"
#include "utils/log_manager.h"

#include <chrono>
#include <iostream>
#include <thread>

// Cross-platform socket handling
#ifdef _WIN32
#include <winsock2.h>
#include <ws2tcpip.h>
#pragma comment(lib, "Ws2_32.lib")
#define CLOSE_SOCKET closesocket
#define SOCKET_TYPE SOCKET
#define INVALID_SOCKET_VALUE INVALID_SOCKET
#define SOCKET_ERROR_VALUE SOCKET_ERROR
#else
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>
#include <fcntl.h>
#define CLOSE_SOCKET close
#define SOCKET_TYPE int
#define INVALID_SOCKET_VALUE -1
#define SOCKET_ERROR_VALUE -1
#endif

namespace core {

// Update WinsockInitializer for cross-platform compatibility
class WinsockInitializer {
public:
    static WinsockInitializer& get_instance() {
        static WinsockInitializer instance;
        return instance;
    }

    bool initialize() {
#ifdef _WIN32
        if (!initialized_) {
            WSADATA wsaData;
            int result = WSAStartup(MAKEWORD(2, 2), &wsaData);
            if (result != 0) {
                LOG_ERROR("WSAStartup failed: " + std::to_string(result));
                return false;
            }
            initialized_ = true;
        }
#endif
        return true;
    }

    ~WinsockInitializer() {
#ifdef _WIN32
        if (initialized_) {
            WSACleanup();
        }
#endif
    }

private:
    WinsockInitializer() : initialized_(false) {
#ifdef _WIN32
        initialize();
#endif
    }

    WinsockInitializer(const WinsockInitializer&) = delete;
    WinsockInitializer& operator=(const WinsockInitializer&) = delete;

    bool initialized_;
};

namespace protocols {

TcpResultProtocol::TcpResultProtocol()
    : running_(false), port_(0), client_count_(0) {
}

TcpResultProtocol::~TcpResultProtocol() {
    stop();
}

bool TcpResultProtocol::start(int port) {
    if (running_) {
        return true; // 已经在运行
    }

    // 设置TCP端口
    port_ = port;

    try {
        // 创建TCP服务器
        tcp_server_ = std::make_unique<TcpServer>(port_,
            [this](std::shared_ptr<TcpConnection> connection) {
                // 处理新连接
                handle_new_connection(connection);
            });

        // 标记为运行状态
        running_ = true;

        // 启动TCP服务线程
        tcp_server_thread_ = std::thread(&TcpResultProtocol::tcp_server_thread_func, this);

        LOG_INFO("TCP协议服务已启动，端口: " + std::to_string(port_));
        return true;
    } catch (const std::exception& e) {
        LOG_ERROR("启动TCP协议服务失败: " + std::string(e.what()));
        return false;
    }
}

void TcpResultProtocol::stop() {
    if (!running_) {
        return;
    }

    // 标记为停止状态
    running_ = false;

    // 停止TCP服务器
    if (tcp_server_) {
        tcp_server_->stop();
        tcp_server_.reset();
    }

    // 等待TCP服务线程结束
    if (tcp_server_thread_.joinable()) {
        tcp_server_thread_.join();
    }

    // 清空连接列表
    {
        std::lock_guard<std::mutex> lock(connections_mutex_);
        connections_.clear();
        client_count_ = 0;
    }

    LOG_INFO("TCP协议服务已停止");
}

bool TcpResultProtocol::send_result(const std::string& json_str) {
    if (!running_) {
        return false;
    }

    // 广播给所有客户端
    broadcast_to_clients(json_str);
    return true;
}

int TcpResultProtocol::get_client_count() const {
    return client_count_;
}

int TcpResultProtocol::get_port() const {
    return port_;
}

std::string TcpResultProtocol::get_protocol_name() const {
    return "TCP";
}

bool TcpResultProtocol::is_running() const {
    return running_;
}

void TcpResultProtocol::set_new_connection_callback(std::function<void(void*)> callback) {
    new_connection_callback_ = callback;
}

void TcpResultProtocol::tcp_server_thread_func() {
    // 服务器已在其内部启动了接受线程，我们只需要等待线程结束
    while (running_) {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
}

void TcpResultProtocol::handle_new_connection(std::shared_ptr<TcpConnection> connection) {
    // 添加到连接列表
    {
        std::lock_guard<std::mutex> lock(connections_mutex_);
        connections_.push_back(connection);
        client_count_ = connections_.size();
    }

    LOG_DEBUG("新的TCP客户端已连接，当前客户端数量: " + std::to_string(client_count_));

    // 调用新连接回调函数
    if (new_connection_callback_) {
        new_connection_callback_(connection.get());
    }
}

void TcpResultProtocol::broadcast_to_clients(const std::string& json_str) {
    // 准备消息，确保以\n结尾
    std::string message_with_newline = json_str;
    if (message_with_newline.empty() || message_with_newline.back() != '\n') {
        message_with_newline += "\n";
    }

    std::lock_guard<std::mutex> lock(connections_mutex_);

    if (connections_.empty()) {
        // 没有客户端连接，直接返回
        return;
    }

    // 移除已断开的连接
    auto it = connections_.begin();
    while (it != connections_.end()) {
        if ((*it)->is_connected()) {
            // 发送数据，确保以\n结尾
            (*it)->send(message_with_newline);
            ++it;
        } else {
            // 连接已断开，移除
            it = connections_.erase(it);
        }
    }

    // 更新客户端计数
    client_count_ = connections_.size();
}

} // namespace protocols
} // namespace core
