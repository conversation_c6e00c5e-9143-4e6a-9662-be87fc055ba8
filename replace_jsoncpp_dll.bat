@echo off
REM 替换 vcpkg jsoncpp.dll 的脚本
REM 使用方法: replace_jsoncpp_dll.bat "C:\path\to\your\jsoncpp.dll"

if "%~1"=="" (
    echo 错误: 请提供您的 jsoncpp.dll 文件路径
    echo 使用方法: %0 "C:\path\to\your\jsoncpp.dll"
    pause
    exit /b 1
)

set YOUR_JSONCPP_DLL=%~1

REM 检查您的 DLL 文件是否存在
if not exist "%YOUR_JSONCPP_DLL%" (
    echo 错误: 文件 "%YOUR_JSONCPP_DLL%" 不存在
    pause
    exit /b 1
)

echo 正在替换 vcpkg 的 jsoncpp.dll...

REM 定义 vcpkg jsoncpp.dll 的位置
set VCPKG_JSONCPP_RELEASE=build\vcpkg_installed\x64-windows\bin\jsoncpp.dll
set VCPKG_JSONCPP_DEBUG=build\vcpkg_installed\x64-windows\debug\bin\jsoncpp.dll

REM 备份原始文件
if exist "%VCPKG_JSONCPP_RELEASE%" (
    echo 备份原始 Release 版本...
    copy "%VCPKG_JSONCPP_RELEASE%" "%VCPKG_JSONCPP_RELEASE%.backup"
    if %ERRORLEVEL% neq 0 (
        echo 备份失败
        pause
        exit /b 1
    )
)

if exist "%VCPKG_JSONCPP_DEBUG%" (
    echo 备份原始 Debug 版本...
    copy "%VCPKG_JSONCPP_DEBUG%" "%VCPKG_JSONCPP_DEBUG%.backup"
    if %ERRORLEVEL% neq 0 (
        echo 备份失败
        pause
        exit /b 1
    )
)

REM 替换 Release 版本
if exist "%VCPKG_JSONCPP_RELEASE%" (
    echo 替换 Release 版本的 jsoncpp.dll...
    copy "%YOUR_JSONCPP_DLL%" "%VCPKG_JSONCPP_RELEASE%"
    if %ERRORLEVEL% neq 0 (
        echo 替换 Release 版本失败
        pause
        exit /b 1
    )
    echo Release 版本替换成功
)

REM 替换 Debug 版本
if exist "%VCPKG_JSONCPP_DEBUG%" (
    echo 替换 Debug 版本的 jsoncpp.dll...
    copy "%YOUR_JSONCPP_DLL%" "%VCPKG_JSONCPP_DEBUG%"
    if %ERRORLEVEL% neq 0 (
        echo 替换 Debug 版本失败
        pause
        exit /b 1
    )
    echo Debug 版本替换成功
)

echo.
echo 替换完成！现在重新构建项目:
echo   cd build
echo   cmake --build . --config Release
echo.
echo 如果需要恢复原始版本，运行:
echo   copy "%VCPKG_JSONCPP_RELEASE%.backup" "%VCPKG_JSONCPP_RELEASE%"
echo   copy "%VCPKG_JSONCPP_DEBUG%.backup" "%VCPKG_JSONCPP_DEBUG%"

pause
