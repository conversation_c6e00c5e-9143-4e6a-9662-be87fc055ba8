#pragma once

#include <string>
#include <vector>
#include <map>
#include <unordered_map>
#include <opencv2/opencv.hpp>
#include <json/json.h>

#include "ai/plugins/task_plugin.h"
#include "tracking/byte_tracker.h"


// Update the export macro to support Linux
#ifdef _WIN32
    #ifdef COUNTING_PLUGIN_EXPORTS
        #define COUNTING_PLUGIN_API __declspec(dllexport)
    #else
        #define COUNTING_PLUGIN_API __declspec(dllimport)
    #endif
#else
    #define COUNTING_PLUGIN_API __attribute__((visibility("default")))
#endif

namespace ai {
namespace plugins {

/**
 * @brief 计数插件，计算穿过计数线的目标数量
 */
class COUNTING_PLUGIN_API CountingPlugin : public TaskPlugin {
public:
    /**
     * @brief 构造函数
     */
    CountingPlugin();

    /**
     * @brief 析构函数
     */
    ~CountingPlugin() override = default;

    /**
     * @brief 初始化插件
     * @return 是否初始化成功
     */
    bool initialize() override;

    /**
     * @brief 批量处理多帧任务
     * @param frames 输入/输出图像列表，包含当前帧及前序帧
     * @param tracks_list 跟踪结果列表，每个元素对应一帧的跟踪结果
     * @param result 处理结果，包含当前帧的处理结果
     * @return 是否处理成功
     */
    bool process_batch(const std::vector<cv::Mat>& frames,
                     const std::vector<std::vector<tracking::strack>>& tracks_list,
                     ai::FrameResult& result) override;

    /**
     * @brief 重置插件状态
     */
    void reset() override;

    /**
     * @brief 获取插件需要处理的帧数
     * @return 需要处理的帧数
     */
    int get_required_frames() const override;

    /**
     * @brief 获取插件类型
     * @return 插件类型
     */
    std::string get_type() const override;

    /**
     * @brief 获取插件描述
     * @return 插件描述
     */
    std::string get_description() const override;

    /**
     * @brief 获取插件版本
     * @return 插件版本
     */
    std::string get_version() const override;

    /**
     * @brief 获取插件作者
     * @return 插件作者
     */
    std::string get_author() const override;



    /**
     * @brief 获取插件默认参数
     * @return 默认参数映射
     */
    std::map<std::string, std::string> get_default_params() const override;

private:
    /**
     * @brief 从参数加载插件配置
     * 重写TaskPlugin::load_parameters方法
     */
    void load_parameters() override;

    /**
     * @brief 创建渲染信息
     * @param frame 当前帧
     * @param tracks 当前帧的跟踪结果
     * @return 渲染信息
     */
    Json::Value create_render_info(const cv::Mat& frame,
                                  const std::vector<tracking::strack>& tracks);

private:
    int required_frames_;                  ///< 需要的帧数
    int frame_count_;                      ///< 帧计数
    int total_count_;                      ///< 总计数
    std::map<std::string, int> class_counts_; ///< 类别计数
    std::map<int, float> track_history_y_; ///< 垂直跟踪历史，用于记录已计数的轨迹
    std::map<int, float> track_history_x_; ///< 水平跟踪历史，用于记录已计数的轨迹
    float line_y_;                   ///< 垂直计数线位置，范围0-1
    float line_x_;                   ///< 水平计数线位置，范围0-1

    // 不再需要单独的params_成员变量，使用基类的params_
};

} // namespace plugins
} // namespace ai

// 导出函数
extern "C" {
    /**
     * @brief 创建任务插件实例
     * @return 插件指针
     */
    COUNTING_PLUGIN_API ai::plugins::TaskPlugin* CreateTaskPlugin();

    /**
     * @brief 销毁任务插件实例
     * @param plugin 插件指针
     */
    COUNTING_PLUGIN_API void DestroyTaskPlugin(ai::plugins::TaskPlugin* plugin);

    /**
     * @brief 获取任务插件API版本
     * @return 插件API版本
     */
    COUNTING_PLUGIN_API int GetTaskPluginApiVersion();
}
