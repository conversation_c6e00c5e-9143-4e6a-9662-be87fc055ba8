#pragma once

#include <string>
#include <boost/log/trivial.hpp>
#include <boost/log/sources/severity_logger.hpp>
#include <boost/log/sources/record_ostream.hpp>
#include <boost/log/utility/setup/file.hpp>
#include <boost/log/utility/setup/console.hpp>
#include <boost/log/utility/setup/common_attributes.hpp>

#include "../aivideocore_export.h"

namespace utils {

/**
 * @brief 日志严重级别枚举
 */
enum class LogLevel {
    Trace,      // 跟踪信息，最详细的日志
    Debug,      // 调试信息
    Info,       // 一般信息
    Warning,    // 警告信息
    Error,      // 错误信息
    Fatal       // 致命错误
};

/**
 * @brief 日志管理器，提供统一的日志接口
 */
class AIVIDEOCORE_API LogManager {
public:
    /**
     * @brief 获取单例实例
     * @return 单例实例引用
     */
    static LogManager& get_instance();

    /**
     * @brief 初始化日志系统
     * @param app_name 应用程序名称
     * @param console_level 控制台日志级别
     * @param file_level 文件日志级别
     * @param log_dir 日志文件目录，默认为"logs"
     */
    void initialize(const std::string& app_name,
                    LogLevel console_level = LogLevel::Info,
                    LogLevel file_level = LogLevel::Debug,
                    const std::string& log_dir = "logs");

    /**
     * @brief 设置控制台日志级别
     * @param level 日志级别
     */
    void set_console_level(LogLevel level);

    /**
     * @brief 设置文件日志级别
     * @param level 日志级别
     */
    void set_file_level(LogLevel level);

    /**
     * @brief 获取当前控制台日志级别
     * @return 日志级别
     */
    LogLevel get_console_level() const;

    /**
     * @brief 获取当前文件日志级别
     * @return 日志级别
     */
    LogLevel get_file_level() const;

    /**
     * @brief 记录跟踪日志
     * @param message 日志消息
     * @param file 源文件名
     * @param line 行号
     */
    void trace(const std::string& message, const char* file = nullptr, int line = 0);

    /**
     * @brief 记录调试日志
     * @param message 日志消息
     * @param file 源文件名
     * @param line 行号
     */
    void debug(const std::string& message, const char* file = nullptr, int line = 0);

    /**
     * @brief 记录信息日志
     * @param message 日志消息
     * @param file 源文件名
     * @param line 行号
     */
    void info(const std::string& message, const char* file = nullptr, int line = 0);

    /**
     * @brief 记录警告日志
     * @param message 日志消息
     * @param file 源文件名
     * @param line 行号
     */
    void warning(const std::string& message, const char* file = nullptr, int line = 0);

    /**
     * @brief 记录错误日志
     * @param message 日志消息
     * @param file 源文件名
     * @param line 行号
     */
    void error(const std::string& message, const char* file = nullptr, int line = 0);

    /**
     * @brief 记录致命错误日志
     * @param message 日志消息
     * @param file 源文件名
     * @param line 行号
     */
    void fatal(const std::string& message, const char* file = nullptr, int line = 0);

    /**
     * @brief 将LogLevel转换为boost::log::trivial::severity_level
     * @param level LogLevel枚举值
     * @return boost日志级别
     */
    static boost::log::trivial::severity_level to_boost_level(LogLevel level);

    /**
     * @brief 将LogLevel转换为字符串
     * @param level LogLevel枚举值
     * @return 日志级别字符串
     */
    static std::string level_to_string(LogLevel level);

    /**
     * @brief 将字符串转换为LogLevel
     * @param level_str 日志级别字符串
     * @return LogLevel枚举值
     */
    static LogLevel string_to_level(const std::string& level_str);

private:
    /**
     * @brief 构造函数
     */
    LogManager();

    /**
     * @brief 析构函数
     */
    ~LogManager();

    /**
     * @brief 确保日志目录存在
     * @param dir_path 目录路径
     * @return 是否成功创建或已存在
     */
    bool ensure_directory_exists(const std::string& dir_path);

private:
    bool initialized_;                    ///< 是否已初始化
    std::string app_name_;                ///< 应用程序名称
    std::string log_dir_;                 ///< 日志目录
    LogLevel console_level_;              ///< 控制台日志级别
    LogLevel file_level_;                 ///< 文件日志级别
    boost::log::sources::severity_logger<boost::log::trivial::severity_level> logger_; ///< 日志记录器
};

} // namespace utils

// 定义日志宏，方便使用
#define LOG_TRACE(message) utils::LogManager::get_instance().trace(message, __FILE__, __LINE__)
#define LOG_DEBUG(message) utils::LogManager::get_instance().debug(message, __FILE__, __LINE__)
#define LOG_INFO(message) utils::LogManager::get_instance().info(message, __FILE__, __LINE__)
#define LOG_WARNING(message) utils::LogManager::get_instance().warning(message, __FILE__, __LINE__)
#define LOG_ERROR(message) utils::LogManager::get_instance().error(message, __FILE__, __LINE__)
#define LOG_FATAL(message) utils::LogManager::get_instance().fatal(message, __FILE__, __LINE__)
