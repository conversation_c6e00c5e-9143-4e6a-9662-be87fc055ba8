@echo off
REM 配置使用动态链接 jsoncpp 的构建脚本

echo 正在配置项目使用动态链接的 jsoncpp...

REM 清理之前的构建
if exist build (
    echo 清理之前的构建目录...
    rmdir /s /q build
)

REM 创建构建目录
mkdir build
cd build

echo 运行 CMake 配置...
REM 使用 vcpkg 的动态链接模式（默认）
cmake .. ^
    -DCMAKE_BUILD_TYPE=Release ^
    -DVCPKG_TARGET_TRIPLET=x64-windows ^
    -DUSE_STATIC_JSONCPP=OFF ^
    -DCMAKE_TOOLCHAIN_FILE=D:/vcpkg/scripts/buildsystems/vcpkg.cmake

if %ERRORLEVEL% neq 0 (
    echo CMake 配置失败
    pause
    exit /b 1
)

echo.
echo 配置完成！现在构建项目:
echo   cmake --build . --config Release
echo.
echo 注意: 使用动态链接后，需要确保 jsoncpp.dll 在运行时可用。

pause
