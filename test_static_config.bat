@echo off
REM 测试静态链接配置的脚本

echo 测试静态链接 jsoncpp 配置...

REM 检查当前目录
if not exist "CMakeLists.txt" (
    echo 错误: 请在项目根目录运行此脚本
    pause
    exit /b 1
)

REM 创建测试构建目录
if exist test_build (
    rmdir /s /q test_build
)
mkdir test_build
cd test_build

echo 正在测试 CMake 配置...
cmake .. -DUSE_STATIC_JSONCPP=ON -DCMAKE_BUILD_TYPE=Release

if %ERRORLEVEL% neq 0 (
    echo CMake 配置失败
    cd ..
    pause
    exit /b 1
)

echo.
echo 配置成功！检查 CMake 输出中的以下信息:
echo - "Configuring jsoncpp for static linking..."
echo - "Using static jsoncpp library: ..."
echo - "Excluding jsoncpp.dll (using static linking)"
echo.

cd ..
echo 清理测试目录...
rmdir /s /q test_build

echo 测试完成！您可以使用以下命令进行完整构建:
echo   configure_static_jsoncpp.bat

pause
