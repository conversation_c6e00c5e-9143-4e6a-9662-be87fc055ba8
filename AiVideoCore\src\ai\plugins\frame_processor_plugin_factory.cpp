#include "ai/plugins/frame_processor_plugin_factory.h"
#include "ai/plugins/frame_processors/motion_blur_processor.h"
#include "ai/plugins/frame_processors/xingpeng_frame_processor.h"
#include <iostream>
#include <filesystem>

namespace ai {
namespace plugins {

FrameProcessorPluginFactory& FrameProcessorPluginFactory::get_instance() {
    static FrameProcessorPluginFactory instance;
    return instance;
}

FrameProcessorPluginFactory::FrameProcessorPluginFactory() {
    // 设置默认插件目录
    plugin_directory_ = "plugins/frame_processors";

    // 确保插件目录存在
    if (!std::filesystem::exists(plugin_directory_)) {
        std::filesystem::create_directories(plugin_directory_);
    }

    // 注册内置插件
    register_builtin_plugins();

    // 加载DLL插件
    load_dll_plugins(plugin_directory_);
}

bool FrameProcessorPluginFactory::register_plugin(const std::string& name, std::function<std::shared_ptr<FrameProcessorPlugin>()> creator) {
    if (plugin_creators_.find(name) != plugin_creators_.end()) {
        return false;
    }

    plugin_creators_[name] = creator;
    return true;
}

std::shared_ptr<FrameProcessorPlugin> FrameProcessorPluginFactory::create_plugin(const std::string& name) {
    auto it = plugin_creators_.find(name);
    if (it == plugin_creators_.end()) {
        return nullptr;
    }

    return it->second();
}

std::vector<std::string> FrameProcessorPluginFactory::get_registered_plugin_names() const {
    std::vector<std::string> names;
    for (const auto& pair : plugin_creators_) {
        names.push_back(pair.first);
    }
    return names;
}

void FrameProcessorPluginFactory::register_builtin_plugins() {
    // 注册运动模糊处理器
    register_plugin("motion_blur", []() {
        auto plugin = std::make_shared<frame_processors::MotionBlurProcessor>();
        return plugin;
    });

    // 注册星鹏帧差累加处理器
    register_plugin("xingpeng_v3", []() {
        auto plugin = std::make_shared<frame_processors::XingpengFrameProcessor>();

        // 确保插件名称与注册名称一致
        if (plugin->get_name() != "xingpeng_v3") {
            plugin->set_name("xingpeng_v3");
        }

        return plugin;
    });

    // 在这里注册其他内置插件
    // ...
}

int FrameProcessorPluginFactory::load_dll_plugins(const std::string& directory) {
    int loaded_count = 0;

    // 检查目录是否存在
    if (!std::filesystem::exists(directory)) {
        std::cout << "Plugin directory does not exist: " << directory << std::endl;
        return 0;
    }

    std::cout << "Loading DLL plugins from directory: " << directory << std::endl;

    // 从目录加载所有DLL插件
    auto plugins = dll_plugin_loader_.load_plugins_from_directory(directory);

    // 注册加载的插件
    for (auto& plugin : plugins) {
        // 创建插件创建函数
        auto creator = [plugin]() {
            return plugin;
        };

        // 注册插件
        std::string name = plugin->get_name();
        if (register_plugin(name, creator)) {
            std::cout << "Registered DLL plugin: " << name << std::endl;
            loaded_count++;
        } else {
            std::cout << "Failed to register DLL plugin: " << name << std::endl;
        }
    }

    std::cout << "Loaded " << loaded_count << " DLL plugins" << std::endl;
    return loaded_count;
}

std::string FrameProcessorPluginFactory::get_plugin_directory() const {
    return plugin_directory_;
}

void FrameProcessorPluginFactory::set_plugin_directory(const std::string& directory) {
    plugin_directory_ = directory;
}

} // namespace plugins
} // namespace ai
